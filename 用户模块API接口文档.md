# 用户模块API接口文档

## 概述

本文档描述了视频用户模块的所有API接口，包括用户注册、登录、信息管理等功能。所有接口都遵循RESTful设计规范。用户数据存储在 `t_video_user` 表中。用户注册后无需邮箱验证，可直接使用。

## 基础信息

- **Base URL**: `http://localhost:8080/api/user`
- **Content-Type**: `application/json`
- **认证方式**: JWT Token (Bearer Token)

## 响应格式

所有接口都返回统一的响应格式：

```json
{
  "code": 200,
  "msg": "success",
  "data": {}
}
```

- `code`: 响应状态码，200表示成功，其他表示失败
- `msg`: 响应消息
- `data`: 响应数据

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 20001 | 用户注册成功 |
| 20002 | 用户登录成功 |
| 20003 | 用户退出成功 |
| 40001 | 邮箱已存在 |
| 40002 | 用户名已存在 |
| 40003 | 用户不存在 |
| 40004 | 密码错误 |
| 40005 | 邮箱格式不正确 |
| 40006 | Token无效或已过期 |

| 40010 | 密码强度不够 |
| 40011 | 账户已被禁用 |
| 40012 | 邮件发送失败 |
| 40013 | 第三方登录失败 |

## 接口列表

### 1. 用户注册

**接口地址**: `POST /api/user/register`

**接口描述**: 用户通过邮箱注册账户

**请求参数**:
```json
{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "Test123456",
  "confirmPassword": "Test123456",
  "nickname": "测试用户",
  "phone": "***********"
}
```

**参数说明**:
- `username`: 用户名，3-20个字符，只能包含字母、数字和下划线
- `email`: 邮箱地址，必须是有效的邮箱格式
- `password`: 密码，6-20个字符，必须包含大小写字母和数字
- `confirmPassword`: 确认密码，必须与password一致
- `nickname`: 昵称，可选，最多50个字符
- `phone`: 手机号，可选，必须是有效的手机号格式

**响应示例**:
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "id": 1,
    "username": "testuser",
    "nickname": "测试用户",
    "email": "<EMAIL>",
    "phone": "***********",
    "avatar": null,
    "status": 1,
    "coins": 0,
    "rewardPoints": 0,
    "loginType": 0,
    "emailVerified": 1,
    "lastLoginTime": null,
    "createTime": "2025-01-01T10:00:00"
  }
}
```

### 2. 用户登录

**接口地址**: `POST /api/user/login`

**接口描述**: 用户通过用户名或邮箱登录

**请求参数**:
```json
{
  "account": "testuser",
  "password": "Test123456",
  "rememberMe": false
}
```

**参数说明**:
- `account`: 登录账号，可以是用户名或邮箱
- `password`: 登录密码
- `rememberMe`: 是否记住登录状态，可选，默认false

**响应示例**:
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "id": 1,
    "username": "testuser",
    "nickname": "测试用户",
    "email": "<EMAIL>",
    "avatar": null,
    "coins": 0,
    "rewardPoints": 0,
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expireTime": "2025-01-08T10:00:00",
    "emailVerified": 1
  }
}
```

### 3. 第三方登录

**接口地址**: `POST /api/user/third-party-login`

**接口描述**: 通过Google或Facebook等第三方平台登录

**请求参数**:
```json
{
  "loginType": 1,
  "authCode": "google_auth_code",
  "thirdPartyId": "google_user_id",
  "email": "<EMAIL>",
  "nickname": "Google User",
  "avatar": "https://example.com/avatar.jpg"
}
```

**参数说明**:
- `loginType`: 登录类型，1=Google, 2=Facebook
- `authCode`: 第三方授权码
- `thirdPartyId`: 第三方用户唯一标识
- `email`: 第三方用户邮箱，可选
- `nickname`: 第三方用户昵称，可选
- `avatar`: 第三方用户头像，可选

**响应示例**: 同用户登录接口

### 4. 获取当前用户信息

**接口地址**: `GET /api/user/info`

**接口描述**: 获取当前登录用户的详细信息

**请求头**:
```
Authorization: Bearer {token}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "id": 1,
    "username": "testuser",
    "nickname": "测试用户",
    "email": "<EMAIL>",
    "phone": "***********",
    "avatar": "https://example.com/avatar.jpg",
    "status": 1,
    "coins": 1000,
    "rewardPoints": 500,
    "loginType": 0,
    "emailVerified": 1,
    "lastLoginTime": "2025-01-01T09:00:00",
    "createTime": "2025-01-01T08:00:00"
  }
}
```

### 5. 更新用户信息

**接口地址**: `PUT /api/user/info`

**接口描述**: 更新当前用户的基本信息

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:
```json
{
  "nickname": "新昵称",
  "email": "<EMAIL>",
  "phone": "13900139000",
  "avatar": "https://example.com/new-avatar.jpg"
}
```

**参数说明**: 所有参数都是可选的，只更新提供的字段

**响应示例**: 同获取用户信息接口

### 6. 修改密码

**接口地址**: `PUT /api/user/password`

**接口描述**: 修改当前用户的登录密码

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:
```json
{
  "oldPassword": "OldPassword123",
  "newPassword": "NewPassword123",
  "confirmNewPassword": "NewPassword123"
}
```

**参数说明**:
- `oldPassword`: 原密码
- `newPassword`: 新密码，6-20个字符，必须包含大小写字母和数字
- `confirmNewPassword`: 确认新密码，必须与newPassword一致

**响应示例**:
```json
{
  "code": 200,
  "msg": "success",
  "data": null
}
```

## 用户字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | Long | 用户ID |
| username | String | 用户名 |
| nickname | String | 昵称 |
| email | String | 邮箱地址 |
| phone | String | 手机号 |
| avatar | String | 头像URL |
| status | Short | 用户状态，0=禁用，1=正常 |
| coins | Long | 金币数量 |
| rewardPoints | Long | 奖励金数量 |
| loginType | Integer | 登录类型，0=普通注册，1=Google，2=Facebook |
| emailVerified | Integer | 邮箱验证状态，0=未验证，1=已验证 |
| lastLoginTime | LocalDateTime | 最后登录时间 |
| createTime | LocalDateTime | 创建时间 |

## 注意事项

1. 除了注册、登录、第三方登录接口外，其他接口都需要在请求头中携带JWT Token
2. JWT Token的有效期为7天
3. 密码必须包含至少一个大写字母、一个小写字母和一个数字
4. 用户名只能包含字母、数字和下划线
5. 新注册用户的初始金币和奖励金都为0
6. 用户注册后邮箱默认为已验证状态，无需邮箱验证
7. 第三方登录用户的邮箱默认为已验证状态
