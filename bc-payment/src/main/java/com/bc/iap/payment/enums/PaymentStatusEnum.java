package com.bc.iap.payment.enums;

import lombok.Getter;

@Getter
public enum PaymentStatusEnum {
    WAIT_PAY(0, "等待支付"),
    SUCCESS_PAY(1, "成功支付"),
    FAIL_PAY(2, "支付错误"),
    TIMEOUT_PAY(4, "超时关闭"),
    UNKNOWN_RESULT(5, "交易结果未知"),
    SYSTEM_EXCEPTION(6, "系统异常"),
    CANCEL_PAY(7, "交易取消"),
    REFUND(8, "退款待审核/取消待处理"),
    REQUIRED_3DS(9, "需要3ds验证"),

    SKIP_PROCESS(10, "跳过处理,已经处理了"),
    ;

    private int status;
    private String remarks;

    PaymentStatusEnum(int status, String remarks) {
        this.status = status;
        this.remarks = remarks;
    }
}
