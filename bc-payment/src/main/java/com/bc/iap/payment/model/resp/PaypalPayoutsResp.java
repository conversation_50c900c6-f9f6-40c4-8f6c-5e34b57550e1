package com.bc.iap.payment.model.resp;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * Auto-generated: 2022-11-17 22:3:21
 *
 * <AUTHOR> 
 * @website http://www.jsons.cn/json2java/ 
 */
@Data
public class PaypalPayoutsResp extends PaypalBaseResp{
    @Getter
    @Setter
    private Integer status;

    @JSONField(name = "batch_header")
    private BatchHeader batchHeader;

    private List<Item> items;

    @Data
    public static class BatchHeader {
        @JSONField(name = "sender_batch_header")
        private SenderBatchHeader senderBatchHeader;
        @JSONField(name = "payout_batch_id")
        private String payoutBatchId;
        @JSONField(name = "batch_status")
        private String batchStatus;
    }
    @Data
    public static class SenderBatchHeader {
        @JSONField(name = "sender_batch_id")
        private String senderBatchId;
        @JSONField(name = "email_subject")
        private String emailSubject;
        @J<PERSON>NField(name = "email_message")
        private String emailMessage;
    }

    @Data
    public static class Item {
        @JSONField(name = "payout_item_id")
        private String payoutItemId;
        @JSONField(name = "transaction_id")
        private String transactionId;
        @JSONField(name = "activity_id")
        private String activityId;
        @JSONField(name = "transaction_status")
        private String transactionStatus;
        @JSONField(name = "payout_batch_id")
        private String payoutBatchId;
        @JSONField(name = "payout_item_fee")
        private PayoutItemFee payoutItemFee;
        @JSONField(name = "payout_item")
        private PayoutItem payoutItem;
        @JSONField(name = "time_processed")
        private Date timeProcessed;
    }

    @Data
    public static class PayoutItemFee {

        private String currency;
        private String value;
    }

    @Data
    public static  class PayoutItem {

        @JSONField(name = "recipient_type")
        private String recipientType;
        private Amount amount;
        private String note;

        private String receiver;

        @JSONField(name = "sender_item_id")
        private Date senderItemId;
    }
    @Data
    public static class Amount {
        private String value;
        private String currency;
    }


}