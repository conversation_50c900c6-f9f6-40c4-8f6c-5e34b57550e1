package com.bc.iap.payment.model.req;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * Payer max payment req
 * Created in 2024.07.04
 *
 * <AUTHOR>
 * @Doc https://docs.payermax.com/api.html?docName=New%20Version&docVer=v1.0&docLang=cn#/paths/aggregate-pay-api-gateway-orderAndPay/post
 */
@NoArgsConstructor
@Data
public class PayerMaxPaymentReq extends PayerMaxPayBaseReq {

    @JSONField(name = "data")
    private PaymentDataDTO data;

    /**
     * Data dto
     * Created in 2024.07.04
     *
     * <AUTHOR>
     */
    @NoArgsConstructor
    @Data
    public static class PaymentDataDTO {
        @JSONField(name = "outTradeNo")
        private String outTradeNo;
        @JSONField(name = "subject")
        private String subject;
        @JSONField(name = "totalAmount")
        private BigDecimal totalAmount;
        @JSONField(name = "currency")
        private String currency;
        @JSONField(name = "country")
        private String country;
        @JSONField(name = "userId")
        private String userId;
        @JSONField(name = "language")
        private String language;
        @JSONField(name = "reference")
        private String reference;
        @JSONField(name = "frontCallbackUrl")
        private String frontCallbackUrl;
        @JSONField(name = "notifyUrl")
        private String notifyUrl;
        @JSONField(name = "integrate")
        private String integrate;
        @JSONField(name = "expireTime")
        private String expireTime;
        @JSONField(name = "subscriptionPlan")
        private SubscriptionPlanDTO subscriptionPlan;
        @JSONField(name = "paymentDetail")
        private PaymentDetailDTO paymentDetail;
        @JSONField(name = "envInfo")
        private EnvInfoDTO envInfo;
        @JSONField(name = "goodsDetails")
        private List<GoodsDetailsDTO> goodsDetails;
        @JSONField(name = "shippingInfo")
        private ShippingInfoDTO shippingInfo;
        @JSONField(name = "billingInfo")
        private BillingInfoDTO billingInfo;
        @JSONField(name = "riskParams")
        private RiskParamsDTO riskParams;
        @JSONField(name = "separateAccountInfo")
        private List<SeparateAccountInfoDTO> separateAccountInfo;

    }

    /**
     * Env info dto
     * Created in 2024.07.04
     *
     * <AUTHOR>
     */
    @NoArgsConstructor
    @Data
    public static class EnvInfoDTO {
        @JSONField(name = "deviceId")
        private String deviceId;
        @JSONField(name = "deviceLanguage")
        private String deviceLanguage;
        @JSONField(name = "screenHeight")
        private Integer screenHeight;
        @JSONField(name = "screenWidth")
        private Integer screenWidth;
    }

    /**
     * Shipping info dto
     * Created in 2024.07.04
     *
     * <AUTHOR>
     */
    @NoArgsConstructor
    @Data
    public static class ShippingInfoDTO {
        @JSONField(name = "firstName")
        private String firstName;
        @JSONField(name = "middleName")
        private String middleName;
        @JSONField(name = "lastName")
        private String lastName;
        @JSONField(name = "phoneNo")
        private String phoneNo;
        @JSONField(name = "email")
        private String email;
        @JSONField(name = "address1")
        private String address1;
        @JSONField(name = "address2")
        private String address2;
        @JSONField(name = "city")
        private String city;
        @JSONField(name = "region")
        private String region;
        @JSONField(name = "state")
        private String state;
        @JSONField(name = "country")
        private String country;
        @JSONField(name = "zipCode")
        private String zipCode;
    }

    /**
     * Billing info dto
     * Created in 2024.07.04
     *
     * <AUTHOR>
     */
    @NoArgsConstructor
    @Data
    public static class BillingInfoDTO {
        @JSONField(name = "firstName")
        private String firstName;
        @JSONField(name = "middleName")
        private String middleName;
        @JSONField(name = "lastName")
        private String lastName;
        @JSONField(name = "email")
        private String email;
        @JSONField(name = "phoneNo")
        private String phoneNo;
        @JSONField(name = "address1")
        private String address1;
        @JSONField(name = "address2")
        private String address2;
        @JSONField(name = "city")
        private String city;
        @JSONField(name = "region")
        private String region;
        @JSONField(name = "state")
        private String state;
        @JSONField(name = "country")
        private String country;
        @JSONField(name = "zipCode")
        private String zipCode;
    }

    /**
     * Risk params dto
     * Created in 2024.07.04
     *
     * <AUTHOR>
     */
    @NoArgsConstructor
    @Data
    public static class RiskParamsDTO {
        @JSONField(name = "registerName")
        private String registerName;
        @JSONField(name = "regTime")
        private String regTime;
        @JSONField(name = "liveCountry")
        private String liveCountry;
        @JSONField(name = "payerAccount")
        private String payerAccount;
        @JSONField(name = "payerName")
        private String payerName;
        @JSONField(name = "taxId")
        private String taxId;
    }

    /**
     * Goods details dto
     * Created in 2024.07.04
     *
     * <AUTHOR>
     */
    @NoArgsConstructor
    @Data
    public static class GoodsDetailsDTO {
        @JSONField(name = "goodsId")
        private String goodsId;
        @JSONField(name = "goodsName")
        private String goodsName;
        @JSONField(name = "quantity")
        private String quantity;
        @JSONField(name = "price")
        private String price;
        @JSONField(name = "goodsCurrency")
        private String goodsCurrency;
        @JSONField(name = "showUrl")
        private String showUrl;
        @JSONField(name = "goodsCategory")
        private String goodsCategory;
    }

    /**
     * Separate account info dto
     * Created in 2024.07.04
     *
     * <AUTHOR>
     */
    @NoArgsConstructor
    @Data
    public static class SeparateAccountInfoDTO {
        @JSONField(name = "participantId")
        private String participantId;
        @JSONField(name = "separateAccountDesc")
        private String separateAccountDesc;
    }

    /**
     * Payment detail dto
     * Created in 2024.07.04
     *
     * <AUTHOR>
     */
    @NoArgsConstructor
    @Data
    public static class PaymentDetailDTO {
        @JSONField(name = "paymentMethodType")
        private String paymentMethodType;
        @JSONField(name = "targetOrg")
        private String targetOrg;
        @JSONField(name = "cardInfo")
        private CardInfoDTO cardInfo;
        @JSONField(name = "payAccountInfo")
        private List<PayAccountInfoDTO> payAccountInfo;
        /**
         * 代表是否MIT交易，是否是商户发起的交易；首次绑定支付方式时，需要用户参与完成认证或授权，传值为false；
         */
        @JSONField(name = "merchantInitiated")
        private boolean merchantInitiated;
        /**
         * 代扣类型；枚举值：SCHEDULED、UNSCHEDULED
         */
        @JSONField(name = "mitType")
        private String mitType;
        /**
         * 是否需要生成token用于后续代扣；首次绑定支付方式时，该值传true；
         */
        @JSONField(name = "tokenForFutureUse")
        private boolean tokenForFutureUse;

        /**
         * Card info dto
         * Created in 2024.07.04
         *
         * <AUTHOR>
         */
        @NoArgsConstructor
        @Data
        public static class CardInfoDTO {
            @JSONField(name = "cardOrg")
            private String cardOrg;
            @JSONField(name = "cardIdentifierNo")
            private String cardIdentifierNo;
            @JSONField(name = "cardHolderFullName")
            private String cardHolderFullName;
            @JSONField(name = "cardExpirationMonth")
            private String cardExpirationMonth;
            @JSONField(name = "cardExpirationYear")
            private String cardExpirationYear;
            @JSONField(name = "cvv")
            private String cvv;
        }

        /**
         * Pay account info dto
         * Created in 2024.07.04
         *
         * <AUTHOR>
         */
        @NoArgsConstructor
        @Data
        public static class PayAccountInfoDTO {
            @JSONField(name = "accountNo")
            private String accountNo;
            @JSONField(name = "accountNoType")
            private String accountNoType;
        }
    }


    @NoArgsConstructor
    @Data
    public static class SubscriptionPlanDTO {
        @JSONField(name = "subscriptionNo")
        private String subscriptionNo;
        @JSONField(name = "subject")
        private String subject;
        @JSONField(name = "description")
        private String description;
        @JSONField(name = "totalPeriods")
        private Integer totalPeriods;
        @JSONField(name = "periodRule")
        private PeriodRuleDTO periodRule;
        @JSONField(name = "periodAmount")
        private PeriodAmountDTO periodAmount;
        @JSONField(name = "trialPeriodConfig")
        private TrialPeriodConfigDTO periodConfigDTO;
        @JSONField(name = "firstPeriodStartDate")
        private String firstPeriodStartDate;
    }

    @NoArgsConstructor
    @Data
    public static class PeriodRuleDTO {
        @JSONField(name = "periodUnit")
        private String periodUnit;
        @JSONField(name = "periodCount")
        private Integer periodCount;
    }

    @NoArgsConstructor
    @Data
    public static class PeriodAmountDTO {
        @JSONField(name = "amount")
        private BigDecimal amount;
        @JSONField(name = "currency")
        private String currency;
    }

    @NoArgsConstructor
    @Data
    public static class TrialPeriodConfigDTO {
        private Integer trialPeriodCount;
        private TrialPeriodAmountDTO trialPeriodAmountDTO = new TrialPeriodAmountDTO();
    }

    @NoArgsConstructor
    @Data
    public static class TrialPeriodAmountDTO {
        @JSONField(name = "amount")
        private BigDecimal amount;
        @JSONField(name = "currency")
        private String currency;
    }
}
