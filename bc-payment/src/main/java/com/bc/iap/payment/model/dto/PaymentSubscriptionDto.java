package com.bc.iap.payment.model.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
public class PaymentSubscriptionDto {
    @JSONField(name = "subject")
    private String subject;
    @JSONField(name = "description")
    private String description;
    @JSONField(name = "totalPeriods")
    private Integer totalPeriods;
    @JSONField(name = "periodRule")
    private PeriodRuleDTO periodRule;
    @JSONField(name = "periodAmount")
    private PeriodAmountDTO periodAmount;
    @JSONField(name = "trialPeriodConfig")
    private TrialPeriodConfigDTO trialPeriodConfig;
    @JSONField(name = "firstPeriodStartDate")
    private String firstPeriodStartDate;


    @NoArgsConstructor
    @Data
    public static class PeriodRuleDTO {
        @JSONField(name = "periodUnit")
        private String periodUnit;
        @J<PERSON>NField(name = "periodCount")
        private Integer periodCount;
    }

    @NoArgsConstructor
    @Data
    public static class PeriodAmountDTO {
        @J<PERSON><PERSON>ield(name = "amount")
        private BigDecimal amount;
        @JSONField(name = "currency")
        private String currency;
    }

    @NoArgsConstructor
    @Data
    public static class TrialPeriodConfigDTO {
        private Integer trialPeriodCount;
        private TrialPeriodAmountDTO trialPeriodAmountDTO;
    }

    @NoArgsConstructor
    @Data
    public static class TrialPeriodAmountDTO {
        @JSONField(name = "amount")
        private BigDecimal amount;
        @JSONField(name = "currency")
        private String currency;
    }
}
