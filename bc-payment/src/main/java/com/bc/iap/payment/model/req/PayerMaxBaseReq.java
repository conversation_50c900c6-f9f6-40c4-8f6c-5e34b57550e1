package com.bc.iap.payment.model.req;

import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * payerMax支付对象
 * <AUTHOR>
 * @date 2021/11/10
 */
@Data
@Builder
@Accessors(chain = true)
public class PayerMaxBaseReq {
    /**
     * 必传,签名
     */
    private String sign;
    /**
     * 必传,接口版本，当前传入“2.0”
     */
    private String version;
    /**
     * 必传,接口业务代码，此处需传入“singlePayment”
     */
    private String bizType;
    /**
     * 流水号
     */
    private String tradeNo;
    /**
     * 必传,PayerMax 分配给商户的唯一标识
     */
    private String merchantId;
    /**
     * 必传,商户订单号，只能包含字母、数字、下划线。每次调用下单时不能重复
     */
    private String orderId;
    /**
     * 非必传,对应所签约付款服务的业务类型， 仅在交易国家为印度时必填：CB
     */
    private String bizModel;
    /**
     * 必传,收款方所在国家代码，大写字母，参见
     * 【https://docs.payermax.com/#/12?page_id=434&si=1&lang=zh-cn】
     */
    private String countryCode;
    /**
     * 必传,付款金额对应货币代码，大写字母, 必须与countryCode字段匹配，
     * 如：印度‘IN’需对应印度卢比‘INR’
     * 见【https://docs.payermax.com/#/12?page_id=434&si=1&lang=zh-cn】
     */
    private String currency;
    /**
     * 必传,付款方式,见 https://docs.payermax.com/#/12?page_id=427&si=1&lang=zh-cn
     */
    private String payType;
    /**
     *  必传,付款金额，小数点后最多支持2位
     */
    private BigDecimal amount;
    /**
     *  必传,收款方账号，根据付款方式不同填入相应账号
     */
    private String payeeAccount;
    /**
     * 非必传, https://docs-server-sg.payermax.com/Public/Uploads/2021-09-18/6145d6f6dd26a.xlsx
     */
    private String payeeAccountType;
    /**
     * 非必传,收款方姓名，支持英文、空格、逗号、中划线、点，在不同国家-支付方式下填写规范不同
     */
    private String payeeName;
    /**
     * 收款方个人识别号码
     */
    private String payeeDocumentId;
    /**
     * 非必传,收款方移动电话号码，在不同国家-支付方式下填写规范不同
     */
    private String payeePhone;
    /**
     * 非必传,最长50,付款方名称（APP名称），
     * 受渠道约束，部分支付方式下收款方可见，若
     * 存在该支付方式需要但商户未传，则会按照规则传递商户在系统内部的显示名称，
     * 超出会截断处理 【如需修改或咨询请联系对应商户支持】
     */
    private String payer;
    /**
     * 非必传,收款方通知邮件地址，在支持通知收款方的支付方式下，可按照规范传入通知地址，不传则默认不通知
     */
    private String notifyAddress;
    /**
     * 非必传,长度100,出款附言或备注，
     * 允许英文，数字，中划线，空格，点；
     * 受渠道约束，该字段可能会进行特殊处理，如超长截断或填充默认值；
     */
    private String transactionNote;
    /**
     * 非必传,取款码有效天数，当前仅在<p>FawryCash</p>中有效，支持传入1~15整数（1=24Hours），其他值或不传则默认为7
     */
    private String expiryDays;
    /**
     * 非必传,是否允许出款给<p>PayTM<p/>未注册用户。
     * 参数为“Y”时，若收款方未注册，则会收到通知，在五日内完成注册，即可收到付款；否则交易失败，金额退还至商户账户。
     * 参数为“N”时，若收款方未注册，交易直接失败，金额退还至商户账户。默认为“N”
     */
    private String payTmAppliedToNewUsers;
    /**
     * 非必传,该字段仅在支付方式为<p>PayTM<p/>PayTM时生效，用于决定商户向【未进行KYC】或【KYC需要完善】的账户打款时，是否立即失败。
     * 参见【付款方式】。
     * 参数为“Y”时，此类交易立即处理为失败；
     * 参数为“N”时，此类交易会按照<p>PayTM<p/>的KYC规则保持支付中状态（3-5天）。
     * 若收款方按照提示进行相应操作，则完成出款；若在规定时间内没有进行相应的操作，则交易失败。
     * 默认为“Y”。
     */
    private String instProcess;
    /**
     * 非必传,透传数据，在付款异步回调通知中原样返回，该字段主要用于商户携带订单的自定义数据
     */
    private String passBackParams;
    /**
     * 非必传,商户接收付款结果的后台回调地址，以http/https开头
     */
    private String callbackUrl;
    private String remark;
}
