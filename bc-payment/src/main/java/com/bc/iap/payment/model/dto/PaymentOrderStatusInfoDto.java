package com.bc.iap.payment.model.dto;

import com.bc.iap.payment.enums.PaymentStatusEnum;
import com.bc.iap.payment.enums.PaymentStatusEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Proxy pay order status info dto
 * Created in 2024.07.11
 *
 * <AUTHOR>
 */
@Data
public class PaymentOrderStatusInfoDto {
    private BigDecimal amount;
    private PaymentStatusEnum statusEnum;
    private String message;
    private String status;
    private Date finishTime;
    private BigDecimal fee;
    private String feeCurrency;
    private String paymentMethodType;
    private String paymentOtherInfo;
}
