package com.bc.iap.payment.model.req;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@NoArgsConstructor
@Data
public class PayerMaxCallbackReq {

    @JSONField(name = "code")
    private String code;
    @JSONField(name = "msg")
    private String msg;
    @JSONField(name = "keyVersion")
    private String keyVersion;
    @JSONField(name = "appId")
    private String appId;
    @JSONField(name = "merchantNo")
    private String merchantNo;
    @JSONField(name = "notifyTime")
    private String notifyTime;
    @JSONField(name = "notifyType")
    private String notifyType;
    @JSONField(name = "data")
    private DataDTO data;


    @NoArgsConstructor
    @Data
    public static class DataDTO {
        @JSONField(name = "outTradeNo")
        private String outTradeNo;
        @J<PERSON>NField(name = "tradeToken")
        private String tradeToken;
        @JSONField(name = "totalAmount")
        private BigDecimal totalAmount;
        @J<PERSON><PERSON>ield(name = "currency")
        private String currency;
        @JSONField(name = "country")
        private String country;
        @JSONField(name = "status")
        private String status;
        @JSONField(name = "completeTime")
        private String completeTime;
        @JSONField(name = "paymentDetails")
        private List<PaymentDetailsDTO> paymentDetails;
        @JSONField(name = "reference")
        private String reference;
        @JSONField(name = "fees")
        private Fee fees;
    }

    @NoArgsConstructor
    @Data
    public static class Fee {
        private MerFee merFee;
    }

    @NoArgsConstructor
    @Data
    public static class MerFee {
        private String url;
        private String amount;
        private String currency;
    }

    @NoArgsConstructor
    @Data
    public static class PaymentDetailsDTO {
        @JSONField(name = "paymentMethodType")
        private String paymentMethodType;
        @JSONField(name = "cardInfo")
        private CardInfo cardInfo;
        @JSONField(name = "targetOrg")
        private String targetOrg;
    }

    @Data
    public static class CardInfo {
        @JSONField(name = "cardOrg")
        private String cardOrg;
        @JSONField(name = "country")
        private String country;
        @JSONField(name = "cardIdentifierNo")
        private String cardIdentifierNo;
        @JSONField(name = "cardIdentifierName")
        private String cardIdentifierName;
    }

}
