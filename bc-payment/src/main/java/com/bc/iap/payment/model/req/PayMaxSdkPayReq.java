package com.bc.iap.payment.model.req;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class PayMaxSdkPayReq extends PayerMaxPayBaseReq {

    @JSONField(name = "data")
    private DataDTO data;

    @NoArgsConstructor
    @Data
    public static class DataDTO {
        @JSONField(name = "totalAmount")
        private Double totalAmount;
        @JSONField(name = "country")
        private String country;
        @JSONField(name = "expireTime")
        private String expireTime;
        @JSONField(name = "paymentDetail")
        private PaymentDetailDTO paymentDetail;
        @JSONField(name = "frontCallbackUrl")
        private String frontCallbackUrl;
        @JSONField(name = "subject")
        private String subject;
        @JSONField(name = "outTradeNo")
        private String outTradeNo;
        @JSONField(name = "notifyUrl")
        private String notifyUrl;
        @J<PERSON>NField(name = "currency")
        private String currency;
        @JSONField(name = "userId")
        private String userId;
        @JSONField(name = "integrate")
        private String integrate;
        @JSONField(name = "terminalType")
        private String terminalType;


    }

    @NoArgsConstructor
    @Data
    public static class PaymentDetailDTO {
        @JSONField(name = "paymentToken")
        private String paymentToken;
        @JSONField(name = "buyerInfo")
        private BuyerInfoDTO buyerInfo;
        @JSONField(name = "sessionKey")
        private String sessionKey;

    }

    @NoArgsConstructor
    @Data
    public static class BuyerInfoDTO {
        @JSONField(name = "clientIp")
        private String clientIp;
        @JSONField(name = "userAgent")
        private String userAgent;
    }
}
