package com.bc.iap.payment.model.resp;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * The type Payer max sub resp.
 */
@NoArgsConstructor
@Data
public class PayerMaxSubResp {

    @JSONField(name = "code")
    private String code;
    @JSONField(name = "msg")
    private String msg;
    @JSONField(name = "data")
    private DataDTO data;

    /**
     * The type Data dto.
     */
    @NoArgsConstructor
    @Data
    public static class DataDTO {
        @JSONField(name = "subscriptionRequestId")
        private String subscriptionRequestId;
        @JSONField(name = "subscriptionPlan")
        private SubscriptionPlanDTO subscriptionPlan;

        /**
         * The type Subscription plan dto.
         */
        @NoArgsConstructor
        @Data
        public static class SubscriptionPlanDTO {
            @JSONField(name = "subscriptionNo")
            private String subscriptionNo;
            @JSONField(name = "subscriptionStatus")
            private String subscriptionStatus;
        }
    }
}
