package com.bc.iap.payment.model.resp;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2021/11/10
 */
@Data
@Accessors(chain = true)
public class PayMaxPayResp {
    private String bizCode;
    private String message;
    private PayMax data;

    @Data
    @Accessors(chain = true)
    public static class PayMax {
        private String orderId;
        private String tradeNo;
    }
}
