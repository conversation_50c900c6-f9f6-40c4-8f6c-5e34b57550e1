package com.bc.iap.payment.enums;

import lombok.Getter;

@Getter
public enum PaymentSubTypeEnum {
    ALL("all", "默认全部"),
    CASH_APP("CASHPAY", "cash pal钱包"),
    CARD("CARD", "信用卡"),
    PIX("PIX", "pix支付"),
    PAYPAL("PAYPAL", "paypal钱包"),
    GOOGLE_PAY("GOOGLEPAY", "google支付"),

    APPLE_PAY("APPLEPAY", "apple支付"),

    KLARNA("KLARNA", "先用后付"),

    LOCAL_CARD("lcreditcard", "本地银行卡"),
    RUSS_PAY("RUSSPAY", "RUSS Pay");
    private String subType;
    private String remarks;

    PaymentSubTypeEnum(String subType, String remarks) {
        this.subType = subType;
        this.remarks = remarks;
    }

    public static PaymentSubTypeEnum getSubTypeByType(String subType) {
        for (PaymentSubTypeEnum subTypeEnum : PaymentSubTypeEnum.values()) {
            if (subTypeEnum.getSubType().equalsIgnoreCase(subType)) {
                return subTypeEnum;
            }
        }
        return null;
    }
}
