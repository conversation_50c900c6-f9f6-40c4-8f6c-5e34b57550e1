package com.bc.iap.payment.service;


import com.bc.iap.payment.enums.CountryEnum;
import com.bc.iap.payment.enums.PaymentTypeEnum;
import com.bc.iap.payment.model.dto.PaymentCallbackInfoDto;
import com.bc.iap.payment.model.dto.PaymentInfoDto;
import com.bc.iap.payment.model.dto.PaymentOrderStatusInfoDto;
import com.bc.iap.payment.model.dto.QueryOrderStatusDto;
import com.bc.iap.payment.enums.CountryEnum;
import com.bc.iap.payment.enums.PaymentTypeEnum;
import com.bc.iap.payment.model.dto.PaymentCallbackInfoDto;
import com.bc.iap.payment.model.dto.PaymentInfoDto;
import com.bc.iap.payment.model.dto.PaymentOrderStatusInfoDto;
import com.bc.iap.payment.model.dto.QueryOrderStatusDto;
import com.bc.iap.payment.model.resp.PaymentRspDto;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

public interface PaymentService {

    PaymentTypeEnum getPaymentType();

    default List<CountryEnum> supportCountry() {
        return null;
    }

    default PaymentRspDto launchPayment(PaymentInfoDto infoDto) {
        return null;
    }

    PaymentOrderStatusInfoDto queryOrderStatus(QueryOrderStatusDto queryOrderDto);

    PaymentCallbackInfoDto paymentCallback(Map<String, Object> paramMap, HttpServletRequest request);

}
