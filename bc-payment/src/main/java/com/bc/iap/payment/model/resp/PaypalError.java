package com.bc.iap.payment.model.resp;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Auto-generated: 2022-11-17 22:3:21
 *
 * <AUTHOR> 
 * @website http://www.jsons.cn/json2java/ 
 */

public class PaypalError {
    public final static String ERROR_VALIDATION_ERROR = "VALIDATION_ERROR";
    @Getter
    @Setter
    private Integer status;
    @Getter
    @Setter
    private String name;
    @Getter
    @Setter
    private String message;
    @Getter
    @Setter
    @JSONField(name = "debug_id")
    private String debugId;
    @Getter
    @Setter
    @JSONField(name = "information_link")
    private String informationLink;
    @Getter
    @Setter
    private List<String> links;

    @Getter
    @Setter
    private List<Detail> details;

    @Data
    public static class Detail {
        private String field;
        private String value;
        private String location;
        private String issue;
        private String description;
    }
    public Map<Integer,String> parserItemError(){
        Map<Integer,String> itemErrorMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(this.details)){
            this.details.forEach(d->{
                if(d.getField().startsWith("items")){
                    String itemIndexStr = getItemIndex(d.getField());
                    if(StringUtils.isNoneBlank(itemIndexStr)){
                        Integer itemIndex = Integer.valueOf(itemIndexStr);
                        String itemError = itemErrorMap.get(itemIndex);
                        if(itemError==null){
                            itemError = Optional.ofNullable(d.getIssue()).orElse(
                                    Optional.ofNullable(d.getDescription()).orElse(""));
                        }else {
                            itemError += ";"+d.getDescription();
                        }
                        itemErrorMap.put(itemIndex,itemError);
                    }
                }
            });
        }
        return itemErrorMap.size()>0?itemErrorMap:null;
    }

    public static String getItemIndex(String str){
        Pattern p = Pattern.compile("(items\\[)([\\w]+)(\\])");
        Matcher m = p.matcher(str);
        while (m.find()) {
            return m.group(2);
        }
        return "";
    }
}