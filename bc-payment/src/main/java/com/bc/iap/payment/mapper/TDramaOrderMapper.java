package com.bc.iap.payment.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bc.iap.payment.model.entity.TDramaOrder;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <p>
 * 短剧订单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
public interface TDramaOrderMapper extends BaseMapper<TDramaOrder> {

    @Select("select * from t_drama_order where user_id=#{userId} and drama_id=#{dramaId} order by create_time desc limit 1")
    TDramaOrder queryOrderByUidAndCid(@Param("userId") Long userId,@Param("dramaId")Long dramaId);

    @Select("select * from t_drama_order where user_id=#{userId} order by create_time desc limit 1")
    TDramaOrder queryOrderByUid(@Param("userId") Long userId);

    @Select("select * from t_drama_order where order_id=#{orderId}")
    TDramaOrder queryOrderByOrderId(@Param("orderId") String orderId);

    @Select("select * from t_drama_order where out_trade_no=#{outTradeNo}")
    TDramaOrder queryOrderByTradeId(@Param("outTradeNo") String outTradeNo);


    @Select("select order_id,pay_platform,merchant_id,out_trade_no from t_drama_order where pay_status=#{status} and create_time>=#{startDate} and create_time<=#{endDate}")
    List<TDramaOrder> queryOrderByStatus(@Param("status") Integer status,
                                             @Param("startDate") String startDate,
                                             @Param("endDate") String endDate);

    @Select("select * from t_drama_order where user_id=#{userId} and app_id=#{appId}")
    List<TDramaOrder> selectOrderByUidAndAid(@Param("userId") Long userId,
                                                 @Param("appId") Long appId);

    int updateOrderInfo(TDramaOrder orderInfo);

    @Update("update t_drama_order set dramaid=#{dramaId} where order_id=#{orderId}")
    int updateOrderDramaId(@Param("dramaId") String dramaId, @Param("orderId") String orderId);

}
