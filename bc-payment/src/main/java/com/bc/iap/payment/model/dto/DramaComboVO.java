package com.bc.iap.payment.model.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class DramaComboVO {

    /**
     * 套餐id
     */
    private Long comboId;

    /**
     * 短剧id,单部剧售价
     */
    private Long dramaId;

    /**
     * 售价
     */
    private BigDecimal sellingPrice;

    /**
     * 国家
     */
    private String country;

    /**
     * 货币
     */
    private String currency;

    /**
     * 订单类型,0剧集,1周卡,2月卡,3季卡,4年卡
     */
    private Integer comboType;

    /**
     * 货币符号
     */
    private String symbol;

    /**
     * 套餐名
     */
    private String comboName;
}
