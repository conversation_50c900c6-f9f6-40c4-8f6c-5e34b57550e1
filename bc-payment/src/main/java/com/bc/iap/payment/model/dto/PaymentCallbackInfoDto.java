package com.bc.iap.payment.model.dto;

import com.bc.iap.payment.enums.PaymentStatusEnum;
import com.bc.iap.payment.enums.PaymentStatusEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Proxy pay callback info dto
 * Created in 2024.07.10
 *
 * <AUTHOR>
 */
@Data
public class PaymentCallbackInfoDto {
    private String merchantId;
    private PaymentStatusEnum statusEnum;
    private String orderId;
    private String orderTradeNo;
    private BigDecimal realAmount = BigDecimal.ZERO;
    private Date finishTime;
    private String transactionType;
    private BigDecimal merFee;
    private String merFeeCurrency;
    private String message;
    private String status;
    private String paymentOtherInfo;
}
