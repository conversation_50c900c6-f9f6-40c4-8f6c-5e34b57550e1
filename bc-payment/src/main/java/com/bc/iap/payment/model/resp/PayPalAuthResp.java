package com.bc.iap.payment.model.resp;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class PayPalAuthResp {

    @JSONField(name = "scope")
    private String scope;
    @JSONField(name = "access_token")
    private String accessToken;
    @JSONField(name = "token_type")
    private String tokenType;
    @JSONField(name = "app_id")
    private String appId;
    @JSONField(name = "expires_in")
    private Integer expiresIn;
    @JSONField(name = "nonce")
    private String nonce;
}
