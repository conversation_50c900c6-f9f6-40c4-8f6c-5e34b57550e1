package com.bc.iap.payment.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryOrderStatusDto {
    private String merchantId;
    private String orderId;
    private String outTradeNo;
    private String current;
    private String amount;

    public QueryOrderStatusDto(String merchantId, String orderId, String outTradeNo) {
        this.merchantId = merchantId;
        this.orderId = orderId;
        this.outTradeNo = outTradeNo;
    }
}
