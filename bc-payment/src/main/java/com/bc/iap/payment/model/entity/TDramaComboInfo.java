package com.bc.iap.payment.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 用户套餐信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Getter
@Setter
@TableName("t_drama_combo_info")
public class TDramaComboInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 产品ID
     */
    private Long appId;

    /**
     * 套餐id
     */
    private Long comboId;

    /**
     * 短剧id,单部剧售价
     */
    private Long dramaId;

    /**
     * 折扣售价
     */
    private BigDecimal discountPrice;

    /**
     * 折扣金额生效几期
     */
    private Integer discountCount;
    /**
     * 售价
     */
    private BigDecimal sellingPrice;

    /**
     * 总期数
     */
    private Integer subscribeTotalPeriods;

    /**
     * 国家
     */
    private String country;

    /**
     * 货币
     */
    private String currency;

    private Integer paymentType;
    /**
     * 订单类型,0剧集,1周卡,2月卡,3季卡,4年卡
     */
    private Integer comboType;

    /**
     * 套餐状态，0开启，1关闭
     */
    private Integer comboStatus;

    /**
     * 套餐描述
     */
    private String description;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
