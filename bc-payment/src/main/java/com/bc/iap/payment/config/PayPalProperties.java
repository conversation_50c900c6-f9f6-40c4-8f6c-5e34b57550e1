package com.bc.iap.payment.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "proxy-pay.paypal")
public class PayPalProperties {
    private String apiUrl;
    private String paypalClientId;
    private String paypalClientSecret;
    private String notifyUrl;

}
