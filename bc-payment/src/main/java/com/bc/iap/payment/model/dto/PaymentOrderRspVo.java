package com.bc.iap.payment.model.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Proxy pay rsp vo
 * Created in 2024.07.10
 *
 * <AUTHOR>
 */
@Data
public class PaymentOrderRspVo {
    
    public PaymentOrderRspVo(List<OrderDataDto> orderList) {
        this.orderList = orderList;
    }

    private List<OrderDataDto> orderList;

    @Data
    public static class OrderDataDto {
        private String orderId;
        private Long dramaId;
        private String currency;
        private Integer orderType;
        private BigDecimal amount;
        private Integer status;
        private Date finishTime;

    }
}
