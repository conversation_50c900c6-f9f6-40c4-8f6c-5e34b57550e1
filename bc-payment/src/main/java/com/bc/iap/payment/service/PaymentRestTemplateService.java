package com.bc.iap.payment.service;

import com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter;
import com.bc.iap.common.utils.QyWxAlarmUtils;
import com.bc.iap.common.utils.RedisKeyUtil;
import com.bc.iap.common.utils.QyWxAlarmUtils;
import com.bc.iap.common.utils.RedisKeyUtil;
import com.bc.iap.payment.constant.PaymentConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Authenticator;
import okhttp3.Credentials;
import okhttp3.OkHttpClient;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.http.client.OkHttp3ClientHttpRequestFactory;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.web.client.ResponseErrorHandler;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class PaymentRestTemplateService {

    @Value("${payment.proxy.ip:}")
    private String proxyIp;

    @Value("${payment.proxy.bak-ip:}")
    private String proxyBakIp;

    @Value("${payment.proxy.port:0}")
    private Integer port;
    @Value("${payment.proxy.username:}")
    private String proxyUsername;
    @Value("${payment.proxy.password:}")
    private String proxyPassword;

    private final RestTemplate restTemplate;

    private RestTemplate proxyProcessErrRestTemplate;
    private RestTemplate proxyMainRestTemplate;

    private RestTemplate proxyBakRestTemplate;


    private final StringRedisTemplate redisTemplate;

    public <T> ResponseEntity<T> postForEntity(String url, @Nullable Object request, Class<T> responseType, Object... uriVariables) {
        try {
            if (isFuseSwitchBak()) {
                return proxyBakRestTemplate.postForEntity(url, request, responseType, uriVariables);
            }
            return proxyMainRestTemplate.postForEntity(url, request, responseType, uriVariables);
        } catch (Exception e) {
            fuseBak();
            throw new RuntimeException("代付代理请求出错！", e);
        }
    }

    public <T> ResponseEntity<T> postForEntityErrReturn(String url, @Nullable Object request, Class<T> responseType, Object... uriVariables) {
        return proxyProcessErrRestTemplate.postForEntity(url, request, responseType, uriVariables);
    }

    public <T> ResponseEntity<T> getForEntityErrReturn(String url, HttpMethod method, HttpEntity<?> requestEntity, Class<T> responseType) {
        return proxyProcessErrRestTemplate.exchange(url, method, requestEntity, responseType);
    }

    public <T> ResponseEntity<T> getForEntityErrReturn(String url, HttpMethod method, HttpEntity<?> requestEntity, Class<T> responseType, Map<String, Object> queryParams) {
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url);
        if (queryParams != null) {
            queryParams.forEach(builder::queryParam);
        }
        URI uri = builder.build().encode().toUri();

        return proxyProcessErrRestTemplate.exchange(uri, method, requestEntity, responseType);
    }


    public <T> ResponseEntity<T> getForEntity(String url, Class<T> responseType, Object... uriVariables) throws Exception {
        try {
            if (isFuseSwitchBak()) {
                return proxyBakRestTemplate.getForEntity(url, responseType, uriVariables);
            }
            return proxyMainRestTemplate.getForEntity(url, responseType, uriVariables);
        } catch (Exception e) {
            fuseBak();
            throw new RuntimeException("代理GET请求出错！", e);
        }
    }


    public <T> ResponseEntity<T> exchange(String url, HttpMethod method, HttpEntity<?> requestEntity, Class<T> responseType) throws Exception {
        try {
            if (isFuseSwitchBak()) {
                return proxyBakRestTemplate.exchange(url, method, requestEntity, responseType);
            }
            return proxyMainRestTemplate.exchange(url, method, requestEntity, responseType);
        } catch (Exception e) {
            fuseBak();
            throw new RuntimeException("代理请求出错！", e);
        }
    }

    public boolean isFuseSwitchBak() {
        return Optional.ofNullable(redisTemplate.hasKey(RedisKeyUtil.buildBakProxyPaySwitchKey())).orElse(false);
    }

    public void fuseBak() {
        //已经切换了备用账号，直接返回
        if (isFuseSwitchBak()) {
            return;
        }
        long errorCount;
        //熔断错误数加1
        if (Boolean.TRUE.equals(redisTemplate.hasKey(RedisKeyUtil.buildProxyPayFuseErrorKey()))) {
            errorCount = Optional.ofNullable(redisTemplate.opsForValue().increment(RedisKeyUtil.buildProxyPayFuseErrorKey())).orElse(0L);
        } else {
            errorCount = Optional.ofNullable(redisTemplate.opsForValue().increment(RedisKeyUtil.buildProxyPayFuseErrorKey())).orElse(0L);
            redisTemplate.expire(RedisKeyUtil.buildProxyPayFuseErrorKey(), 10, TimeUnit.MINUTES);
        }
        if (errorCount >= 3) {
            redisTemplate.opsForValue().increment(RedisKeyUtil.buildBakProxyPaySwitchKey());
            sendFuseOpenBakWarn();
        }
    }

    private void sendFuseOpenBakWarn() {
        QyWxAlarmUtils.sendWarn("代理主ip生成代付订单失败，切换至备用代理ip！", "过去10分钟使用代理主ip生成代付订单失败已达3次，自动切换至备用代理ip，请检查代理", PaymentConstant.WECHAT_ROBOT_SEND_URL);
    }

    @PostConstruct
    public void init() {
        proxyMainRestTemplate = proxyMainRestTemplate();
        proxyBakRestTemplate = proxyBakRestTemplate();
        proxyProcessErrRestTemplate = proxyProcessErrRestTemplate();
    }

    private RestTemplate proxyProcessErrRestTemplate() {
        OkHttp3ClientHttpRequestFactory factory = new OkHttp3ClientHttpRequestFactory(okHttpConfigClient(proxyIp));
        factory.setReadTimeout(5000);//ms
        factory.setConnectTimeout(5000);//ms

        RestTemplate restTemplate = new RestTemplate(factory);
        //异常不抛，返回响应，paypal需要错误处理
        ResponseErrorHandler responseErrorHandler = new ResponseErrorHandler() {
            @Override
            public boolean hasError(ClientHttpResponse response) throws IOException {
                return true;
            }

            @Override
            public void handleError(ClientHttpResponse response) throws IOException {
            }
        };
        restTemplate.setErrorHandler(responseErrorHandler);

        //重新设置StringHttpMessageConverter字符集为UTF-8，解决中文乱码问题
        List<HttpMessageConverter<?>> converterList = restTemplate.getMessageConverters();
        converterList.removeIf(item -> item.getClass() == StringHttpMessageConverter.class || item.getClass() == MappingJackson2HttpMessageConverter.class);
        converterList.add(0, new StringHttpMessageConverter(StandardCharsets.UTF_8));
        //加入 FastJson转换器
        converterList.add(configureFastJsonHttpMessageConverter());

        return restTemplate;
    }

    private RestTemplate proxyMainRestTemplate() {
        OkHttp3ClientHttpRequestFactory factory = new OkHttp3ClientHttpRequestFactory(okHttpConfigClient(proxyIp));
        factory.setReadTimeout(5000);//ms
        factory.setConnectTimeout(5000);//ms

        RestTemplate restTemplate = new RestTemplate(factory);

        //重新设置StringHttpMessageConverter字符集为UTF-8，解决中文乱码问题
        List<HttpMessageConverter<?>> converterList = restTemplate.getMessageConverters();
        converterList.removeIf(item -> item.getClass() == StringHttpMessageConverter.class || item.getClass() == MappingJackson2HttpMessageConverter.class);
        converterList.add(0, new StringHttpMessageConverter(StandardCharsets.UTF_8));
        //加入 FastJson转换器
        converterList.add(configureFastJsonHttpMessageConverter());

        return restTemplate;
    }

    private RestTemplate proxyBakRestTemplate() {
        OkHttp3ClientHttpRequestFactory factory = new OkHttp3ClientHttpRequestFactory(okHttpConfigClient(proxyBakIp));
        factory.setReadTimeout(5000);//ms
        factory.setConnectTimeout(5000);//ms

        RestTemplate restTemplate = new RestTemplate(factory);

        //重新设置StringHttpMessageConverter字符集为UTF-8，解决中文乱码问题
        List<HttpMessageConverter<?>> converterList = restTemplate.getMessageConverters();
        converterList.removeIf(item -> item.getClass() == StringHttpMessageConverter.class || item.getClass() == MappingJackson2HttpMessageConverter.class);
        converterList.add(0, new StringHttpMessageConverter(StandardCharsets.UTF_8));
        //加入 FastJson转换器
        converterList.add(configureFastJsonHttpMessageConverter());

        return restTemplate;
    }

    public OkHttpClient okHttpConfigClient(String ip) {
        if (StringUtils.isBlank(ip)) {
            return new OkHttpClient().newBuilder()
                    .build();
        }
        Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(proxyIp, port));
        Authenticator proxyAuthenticator = (route, response) -> {
            String credential = Credentials.basic(proxyUsername, proxyPassword);
            return response.request().newBuilder()
                    .header("Proxy-Authorization", credential)
                    .build();
        };
        return new OkHttpClient().newBuilder()
                // 设置代理
                .proxy(proxy)
                .proxyAuthenticator(proxyAuthenticator)
                .authenticator(proxyAuthenticator)
                .build();
    }

    private FastJsonHttpMessageConverter configureFastJsonHttpMessageConverter() {
        FastJsonHttpMessageConverter fastJsonConverter = new FastJsonHttpMessageConverter();
        List<MediaType> supportedMediaTypes = new ArrayList<>();
        supportedMediaTypes.add(MediaType.APPLICATION_JSON);
        supportedMediaTypes.add(MediaType.APPLICATION_ATOM_XML);
        supportedMediaTypes.add(MediaType.APPLICATION_FORM_URLENCODED);
        supportedMediaTypes.add(MediaType.APPLICATION_OCTET_STREAM);
        supportedMediaTypes.add(MediaType.APPLICATION_PDF);
        supportedMediaTypes.add(MediaType.APPLICATION_RSS_XML);
        supportedMediaTypes.add(MediaType.APPLICATION_XHTML_XML);
        supportedMediaTypes.add(MediaType.APPLICATION_XML);
        supportedMediaTypes.add(MediaType.IMAGE_GIF);
        supportedMediaTypes.add(MediaType.IMAGE_JPEG);
        supportedMediaTypes.add(MediaType.IMAGE_PNG);
        supportedMediaTypes.add(MediaType.TEXT_EVENT_STREAM);
        supportedMediaTypes.add(MediaType.TEXT_HTML);
        supportedMediaTypes.add(MediaType.TEXT_MARKDOWN);
        supportedMediaTypes.add(MediaType.TEXT_PLAIN);
        supportedMediaTypes.add(MediaType.TEXT_XML);
        fastJsonConverter.setSupportedMediaTypes(supportedMediaTypes);
        return fastJsonConverter;
    }
}
