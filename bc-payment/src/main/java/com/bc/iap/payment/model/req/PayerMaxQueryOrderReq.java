package com.bc.iap.payment.model.req;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class PayerMaxQueryOrderReq extends PayerMaxPayBaseReq {

    @JSONField(name = "data")
    private DataDTO data;

    @NoArgsConstructor
    @Data
    public static class DataDTO {
        @JSONField(name = "outTradeNo")
        private String outTradeNo;
    }
}
