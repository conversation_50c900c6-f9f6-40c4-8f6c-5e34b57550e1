package com.bc.iap.payment.model.resp;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@NoArgsConstructor
@Data
public class PayerMaxOrderInfoResp {

    @JSONField(name = "msg")
    private String msg;
    @JSONField(name = "code")
    private String code;
    @JSONField(name = "data")
    private DataDTO data;

    @NoArgsConstructor
    @Data
    public static class DataDTO {
        @JSONField(name = "reference")
        private String reference;
        @JSONField(name = "country")
        private String country;
        @JSONField(name = "totalAmount")
        private BigDecimal totalAmount;
        @JSONField(name = "outTradeNo")
        private String outTradeNo;
        @JSONField(name = "currency")
        private String currency;
        @JSONField(name = "channelNo")
        private String channelNo;
        @JSONField(name = "thirdChannelNo")
        private String thirdChannelNo;
        @JSONField(name = "paymentCode")
        private String paymentCode;
        @JSONField(name = "tradeToken")
        private String tradeToken;
        @JSONField(name = "completeTime")
        private String completeTime;
        @JSONField(name = "paymentDetails")
        private List<PaymentDetailsDTO> paymentDetails;
        @JSONField(name = "fees")
        private FeesDTO fees;
        @JSONField(name = "status")
        private String status;
        @JSONField(name = "resultMsg")
        private String resultMsg;
    }

    @NoArgsConstructor
    @Data
    public static class FeesDTO {
        @JSONField(name = "merFee")
        private MerFeeDTO merFee;

    }

    @NoArgsConstructor
    @Data
    public static class MerFeeDTO {
        @JSONField(name = "url")
        private String url;
        @JSONField(name = "amount")
        private String amount;
        @JSONField(name = "currency")
        private String currency;
    }

    @NoArgsConstructor
    @Data
    public static class PaymentDetailsDTO {
        @JSONField(name = "targetOrg")
        private String targetOrg;
        @JSONField(name = "cardInfo")
        private CardInfoDTO cardInfo;
        @JSONField(name = "payAmount")
        private Integer payAmount;
        @JSONField(name = "exchangeRate")
        private String exchangeRate;
        @JSONField(name = "paymentMethod")
        private String paymentMethod;
        @JSONField(name = "payCurrency")
        private String payCurrency;
        @JSONField(name = "paymentMethodType")
        private String paymentMethodType;


    }

    @NoArgsConstructor
    @Data
    public static class CardInfoDTO {
        @JSONField(name = "cardOrg")
        private String cardOrg;
        @JSONField(name = "country")
        private String country;
        @JSONField(name = "cardIdentifierNo")
        private String cardIdentifierNo;
        @JSONField(name = "cardIdentifierName")
        private String cardIdentifierName;
    }
}
