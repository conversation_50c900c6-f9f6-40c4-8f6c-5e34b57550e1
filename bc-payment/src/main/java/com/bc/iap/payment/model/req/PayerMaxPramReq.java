package com.bc.iap.payment.model.req;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2021/11/12
 */
@Data
@Accessors(chain = true)
public class PayerMaxPramReq {
    private String orderId;
    private String callbackUrl;
    private String payerName;
    private String payerMaxUrl;
    private String merchantId;
    private String secretKey;
    private String appId;
    /**
     * 商户私钥
     */
    private String merchantPrivateKey;
    /**
     * 平台公钥
     */
    private String pmPublicKey;
    /**
     * 透传参数
     */
    private String passBackParams;

    private String note;
}
