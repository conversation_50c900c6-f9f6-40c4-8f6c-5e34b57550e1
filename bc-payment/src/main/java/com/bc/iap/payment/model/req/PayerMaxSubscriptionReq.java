package com.bc.iap.payment.model.req;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@NoArgsConstructor
@Data
public class PayerMaxSubscriptionReq extends PayerMaxPayBaseReq {

    @JSONField(name = "data")
    private SubscriptionDataDTO data;

    @NoArgsConstructor
    @Data
    public static class SubscriptionDataDTO {
        @JSONField(name = "subscriptionRequestId")
        private String subscriptionRequestId;
        @JSONField(name = "userId")
        private String userId;
        @JSONField(name = "callbackUrl")
        private String callbackUrl;
        @JSONField(name = "subscriptionPlan")
        private SubscriptionPlanDTO subscriptionPlan;

    }


    @NoArgsConstructor
    @Data
    public static class SubscriptionPlanDTO {
        @JSONField(name = "subject")
        private String subject;
        @JSONField(name = "description")
        private String description;
        @JSONField(name = "totalPeriods")
        private Integer totalPeriods;
        @JSONField(name = "periodRule")
        private PeriodRuleDTO periodRule;
        @JSONField(name = "periodAmount")
        private PeriodAmountDTO periodAmount;
        @JSONField(name = "trialPeriodConfig")
        private TrialPeriodConfigDTO periodConfigDTO;
        @JSONField(name = "firstPeriodStartDate")
        private String firstPeriodStartDate;
        /**
         * 非必填、指定后续每期的提前扣款天数
         */
        @JSONField(name = "advanceDays")
        private Integer advanceDays;
    }
    @NoArgsConstructor
    @Data
    public static class PeriodRuleDTO {
        @JSONField(name = "periodUnit")
        private String periodUnit;
        @JSONField(name = "periodCount")
        private Integer periodCount;
    }

    @NoArgsConstructor
    @Data
    public static class PeriodAmountDTO {
        @JSONField(name = "amount")
        private BigDecimal amount;
        @JSONField(name = "currency")
        private String currency;
    }


    @NoArgsConstructor
    @Data
    public static class TrialPeriodConfigDTO {
        private Integer trialPeriodCount;
        private TrialPeriodAmountDTO trialPeriodAmount = new TrialPeriodAmountDTO();
    }

    @NoArgsConstructor
    @Data
    public static class TrialPeriodAmountDTO {
        @JSONField(name = "amount")
        private BigDecimal amount;
        @JSONField(name = "currency")
        private String currency;
    }
}
