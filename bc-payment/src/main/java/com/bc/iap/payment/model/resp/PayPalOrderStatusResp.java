package com.bc.iap.payment.model.resp;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@NoArgsConstructor
@Data
public class PayPalOrderStatusResp {

    @JSONField(name = "id")
    private String id;
    @JSONField(name = "intent")
    private String intent;
    @JSONField(name = "status")
    private String status;
    @JSONField(name = "payment_source")
    private Map<String, Object> paymentSource;
    @JSONField(name = "purchase_units")
    private List<PurchaseUnitsDTO> purchaseUnits;
    @J<PERSON>NField(name = "create_time")
    private String createTime;
    @JSONField(name = "update_time")
    private String updateTime;
    @JSONField(name = "links")
    private List<LinksDTO> links;

    @JSONField(name = "details")
    private List<PaypalCaptureResp.DetailsDTO> details;


    @NoArgsConstructor
    @Data
    public static class PurchaseUnitsDTO {
        @JSONField(name = "reference_id")
        private String referenceId;
        @JSONField(name = "amount")
        private AmountDTO amount;
        @JSONField(name = "payee")
        private PayeeDTO payee;

    }

    @NoArgsConstructor
    @Data
    public static class AmountDTO {
        @JSONField(name = "currency_code")
        private String currencyCode;
        @JSONField(name = "value")
        private String value;
    }

    @NoArgsConstructor
    @Data
    public static class PayeeDTO {
        @JSONField(name = "email_address")
        private String emailAddress;
        @JSONField(name = "merchant_id")
        private String merchantId;
        @JSONField(name = "display_data")
        private DisplayDataDTO displayData;

    }

    @NoArgsConstructor
    @Data
    public static class DisplayDataDTO {
        @JSONField(name = "brand_name")
        private String brandName;
    }

    @NoArgsConstructor
    @Data
    public static class LinksDTO {
        @JSONField(name = "href")
        private String href;
        @JSONField(name = "rel")
        private String rel;
        @JSONField(name = "method")
        private String method;
    }
}
