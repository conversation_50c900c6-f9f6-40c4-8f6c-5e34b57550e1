package com.bc.iap.payment.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "payment.payermax")
public class PayerMaxProperties {

    private String apiUrl;
    private String appId;

    private String pmPublicKey;

    private String merchantPrivateKey;

    private String merchantId;

    private String notifyUrl;

    private String frontCallbackUrl;

}
