package com.bc.iap.payment.model.resp;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class PaypalCaptureResp {
    @J<PERSON>NField(name = "id")
    private String id;
    @JSO<PERSON>ield(name = "status")
    private String status;
    @JSONField(name = "payment_source")
    private PaymentSourceDTO paymentSource;
    @JSONField(name = "purchase_units")
    private List<PurchaseUnitsDTO> purchaseUnits;
    @JSONField(name = "payer")
    private PayerDTO payer;
    @JSONField(name = "links")
    private List<LinksDTO> links;
    @JSONField(name = "name")
    private String name;
    @JSO<PERSON>ield(name = "details")
    private List<DetailsDTO> details;


    @NoArgsConstructor
    @Data
    public static class PaymentSourceDTO {
        @JSONField(name = "paypal")
        private PaypalDTO paypal;

        @<PERSON><PERSON><PERSON>ield(name = "card")
        private PaypalDTO card;

    }

    @Data
    public static class CardDTO {
        @J<PERSON>NField(name = "name")
        private String name;
        @J<PERSON><PERSON>ield(name = "last_digits")
        private String lastDigits;
        @J<PERSON><PERSON>ield(name = "expiry")
        private String expiry;
        @JSONField(name = "brand")
        private String brand;
        @JSONField(name = "available_networks")
        private List<String> availableNetworks;
        @JSONField(name = "type")
        private String type;
    }

    @NoArgsConstructor
    @Data
    public static class PaypalDTO {
        @JSONField(name = "email_address")
        private String emailAddress;
        @JSONField(name = "account_id")
        private String accountId;
        @JSONField(name = "account_status")
        private String accountStatus;
        @JSONField(name = "name")
        private NameDTO name;
        @JSONField(name = "address")
        private AddressDTO address;

    }

    @NoArgsConstructor
    @Data
    public static class PayerDTO {
        @JSONField(name = "name")
        private NameDTO name;
        @JSONField(name = "email_address")
        private String emailAddress;
        @JSONField(name = "payer_id")
        private String payerId;
        @JSONField(name = "address")
        private AddressDTO address;
        @JSONField(name = "capture_id")
        private String captureId;


    }

    @NoArgsConstructor
    @Data
    public static class NameDTO {
        @JSONField(name = "given_name")
        private String givenName;
        @JSONField(name = "surname")
        private String surname;
        @JSONField(name = "full_name")
        private String fullName;
    }

    @NoArgsConstructor
    @Data
    public static class PurchaseUnitsDTO {
        @JSONField(name = "reference_id")
        private String referenceId;
        @JSONField(name = "shipping")
        private ShippingDTO shipping;
        @JSONField(name = "payments")
        private PaymentsDTO payments;
    }

    @NoArgsConstructor
    @Data
    public static class PaymentsDTO {
        @JSONField(name = "captures")
        private List<CapturesDTO> captures;
    }

    @NoArgsConstructor
    @Data
    public static class ShippingDTO {
        @JSONField(name = "name")
        private NameDTO name;
        @JSONField(name = "address")
        private AddressDTO address;


    }

    @NoArgsConstructor
    @Data
    public static class AddressDTO {
        @JSONField(name = "address_line_1")
        private String addressLine1;
        @JSONField(name = "address_line_2")
        private String addressLine2;
        @JSONField(name = "admin_area_2")
        private String adminArea2;
        @JSONField(name = "admin_area_1")
        private String adminArea1;
        @JSONField(name = "postal_code")
        private String postalCode;
        @JSONField(name = "country_code")
        private String countryCode;
    }

    @NoArgsConstructor
    @Data
    public static class CapturesDTO {
        @JSONField(name = "id")
        private String id;
        @JSONField(name = "status")
        private String status;
        @JSONField(name = "amount")
        private AmountDTO amount;
        @JSONField(name = "final_capture")
        private Boolean finalCapture;
        @JSONField(name = "seller_protection")
        private SellerProtectionDTO sellerProtection;
        @JSONField(name = "seller_receivable_breakdown")
        private SellerReceivableBreakdownDTO sellerReceivableBreakdown;
        @JSONField(name = "links")
        private List<LinksDTO> links;
        @JSONField(name = "create_time")
        private String createTime;
        @JSONField(name = "update_time")
        private String updateTime;


        @NoArgsConstructor
        @Data
        public static class LinksDTO {
            @JSONField(name = "href")
            private String href;
            @JSONField(name = "rel")
            private String rel;
            @JSONField(name = "method")
            private String method;
        }
    }

    @NoArgsConstructor
    @Data
    public static class AmountDTO {
        @JSONField(name = "currency_code")
        private String currencyCode;
        @JSONField(name = "value")
        private String value;
    }

    @NoArgsConstructor
    @Data
    public static class SellerProtectionDTO {
        @JSONField(name = "status")
        private String status;
    }

    @NoArgsConstructor
    @Data
    public static class SellerReceivableBreakdownDTO {
        @JSONField(name = "gross_amount")
        private GrossAmountDTO grossAmount;
        @JSONField(name = "paypal_fee")
        private PaypalFeeDTO paypalFee;
        @JSONField(name = "net_amount")
        private NetAmountDTO netAmount;
        @JSONField(name = "receivable_amount")
        private ReceivableAmountDTO receivableAmount;
        @JSONField(name = "exchange_rate")
        private ExchangeRateDTO exchangeRate;


    }

    @NoArgsConstructor
    @Data
    public static class GrossAmountDTO {
        @JSONField(name = "currency_code")
        private String currencyCode;
        @JSONField(name = "value")
        private String value;
    }

    @NoArgsConstructor
    @Data
    public static class PaypalFeeDTO {
        @JSONField(name = "currency_code")
        private String currencyCode;
        @JSONField(name = "value")
        private String value;
    }

    @NoArgsConstructor
    @Data
    public static class NetAmountDTO {
        @JSONField(name = "currency_code")
        private String currencyCode;
        @JSONField(name = "value")
        private String value;
    }

    @NoArgsConstructor
    @Data
    public static class ReceivableAmountDTO {
        @JSONField(name = "currency_code")
        private String currencyCode;
        @JSONField(name = "value")
        private String value;
    }

    @NoArgsConstructor
    @Data
    public static class ExchangeRateDTO {
        @JSONField(name = "source_currency")
        private String sourceCurrency;
        @JSONField(name = "target_currency")
        private String targetCurrency;
        @JSONField(name = "value")
        private String value;
    }

    @NoArgsConstructor
    @Data
    public static class LinksDTO {
        @JSONField(name = "href")
        private String href;
        @JSONField(name = "rel")
        private String rel;
        @JSONField(name = "method")
        private String method;
    }

    @NoArgsConstructor
    @Data
    public static class DetailsDTO {
        @JSONField(name = "issue")
        private String issue;
        @JSONField(name = "description")
        private String description;
    }
}
