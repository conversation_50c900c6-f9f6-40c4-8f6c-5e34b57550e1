package com.bc.iap.payment.model.req;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class PayerMaxPayBaseReq {
    @JSONField(name = "version")
    private String version;
    @JSONField(name = "keyVersion")
    private String keyVersion;
    @JSONField(name = "requestTime")
    private String requestTime;
    @JSONField(name = "appId")
    private String appId;
    @JSONField(name = "merchantNo")
    private String merchantNo;
}
