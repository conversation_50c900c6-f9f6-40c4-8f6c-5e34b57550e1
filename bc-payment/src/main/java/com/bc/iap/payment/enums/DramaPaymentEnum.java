package com.bc.iap.payment.enums;

import lombok.Getter;

@Getter
public enum DramaPaymentEnum {
    ;
    private int type;
    private String remark;

    DramaPaymentEnum(int type, String remark) {
        this.type = type;
        this.remark = remark;
    }

    public static DramaPaymentEnum getSubTypeByType(int type) {
        for (DramaPaymentEnum typeEnum : DramaPaymentEnum.values()) {
            if (typeEnum.getType() == type) {
                return typeEnum;
            }
        }
        return null;
    }
}
