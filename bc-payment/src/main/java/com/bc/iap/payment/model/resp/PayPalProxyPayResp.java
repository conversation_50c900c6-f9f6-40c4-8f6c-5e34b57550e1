package com.bc.iap.payment.model.resp;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class PayPalProxyPayResp {

    @JSONField(name = "id")
    private String id;
    @JSONField(name = "links")
    private List<LinksDTO> links;
    @JSONField(name = "status")
    private String status;

    @NoArgsConstructor
    @Data
    public static class LinksDTO {
        @JSONField(name = "href")
        private String href;
        @JSONField(name = "method")
        private String method;
        @JSONField(name = "rel")
        private String rel;
    }
}
