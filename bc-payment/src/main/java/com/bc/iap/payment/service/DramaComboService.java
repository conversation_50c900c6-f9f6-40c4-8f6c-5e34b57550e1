package com.bc.iap.payment.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bc.iap.common.utils.RequestInfoThreadLocal;
import com.bc.iap.payment.enums.CountryEnum;
import com.bc.iap.payment.mapper.TDramaComboInfoMapper;
import com.bc.iap.payment.model.dto.DramaComboVO;
import com.bc.iap.payment.model.entity.TDramaComboInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Slf4j
@Component
public class DramaComboService {
    private final TDramaComboInfoMapper tDramaComboInfoMapper;

    public List<DramaComboVO> getComboInfoList() {
        QueryWrapper<TDramaComboInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("app_id", Arrays.asList(RequestInfoThreadLocal.getAppId(),0L));
        queryWrapper.eq("combo_status", 0);
        List<TDramaComboInfo> dramaComboInfos = tDramaComboInfoMapper.selectList(queryWrapper);
        return dramaComboInfos.stream().map(x -> {
            DramaComboVO comboVO = new DramaComboVO();
            BeanUtils.copyProperties(x, comboVO);
            comboVO.setSymbol(CountryEnum.getCountryEnum(x.getCountry()).getSymbol());
            comboVO.setComboName(x.getDescription());
            return comboVO;
        }).collect(Collectors.toList());
    }
}
