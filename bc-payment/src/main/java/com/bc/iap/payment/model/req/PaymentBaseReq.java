package com.bc.iap.payment.model.req;

import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class PaymentBaseReq {
    /**
     * 套餐ID
     */
    @NotNull(message = "comboId not null")
    private Long comboId;
    /**
     * 短剧ID
     */
    private Long dramaId;
    /**
     * 支付类型
     */
    private String paymentType;
    /**
     * 支付成功调整链接
     */
    private String syncUrl;
}
