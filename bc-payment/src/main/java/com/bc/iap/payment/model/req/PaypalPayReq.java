package com.bc.iap.payment.model.req;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@NoArgsConstructor
@Data
public class PaypalPayReq {

    @JSONField(name = "intent")
    private String intent;
    @JSONField(name = "purchase_units")
    private List<PurchaseUnitsDTO> purchaseUnits;
    @JSONField(name = "payment_source")
    private Map<String, PaymentSource> paymentSource;

    public static class PaymentSource {
    }

    @NoArgsConstructor
    @Data
    public static class PurchaseUnitsDTO {
        @JSONField(name = "reference_id")
        private String referenceId;
        @JSONField(name = "invoice_id")
        private String invoiceId;
        @JSONField(name = "amount")
        private AmountDTO amount;
    }

    @NoArgsConstructor
    @Data
    public static class AmountDTO {
        @JSONField(name = "currency_code")
        private String currencyCode;
        @JSONField(name = "value")
        private String value;
    }
}
