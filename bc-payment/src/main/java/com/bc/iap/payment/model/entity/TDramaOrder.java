package com.bc.iap.payment.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 短剧订单表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Getter
@Setter
@TableName("t_drama_order")
public class TDramaOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 产品id
     */
    private Long appId;

    /**
     * 关联用户
     */
    private Long userId;

    /**
     * 订单类型,0剧集,1月卡,2季卡,3年卡
     */
    private Integer orderType;

    /**
     * 关联短剧ID,买的剧集
     */
    private Long dramaId;

    /**
     * 用户ip
     */
    private String ip;

    /**
     * 国家
     */
    private String country;

    /**
     * 货币单位
     */
    private String currency;

    /**
     * 平台商户id
     */
    private String merchantId;

    /**
     * 支付第三方平台,paypal、paymax、dasheng
     */
    private String payPlatform;

    /**
     * 请求收款金额
     */
    private BigDecimal amount;

    /**
     * 美元值
     */
    private BigDecimal usdValue;

    /**
     * 实际收款金额
     */
    private BigDecimal realAmount;

    /**
     * 支付状态,0待付款,1付款成功,2付款失败,3超时关闭
     */
    private Integer payStatus;

    /**
     * 订单返回信息
     */
    private String orderMessage;

    /**
     * 订单备注
     */
    private String remarks;

    /**
     * 付款统计信息
     */
    private String paymentStatInfo;

    /**
     * 平台订单号
     */
    private String outTradeNo;

    /**
     * 用户支付方式类型
     */
    private String paymentMethodType;

    /**
     * 卡信息
     */
    private String paymentOtherInfo;

    /**
     * 订单完成时间
     */
    private Date orderFinishTime;

    /**
     * 手续费
     */
    private BigDecimal merFee;

    /**
     * 手续费货币
     */
    private String merFeeCurrency;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
