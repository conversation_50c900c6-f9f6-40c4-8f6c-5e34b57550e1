package com.bc.iap.payment.model.dto;

import com.bc.iap.payment.enums.CountryEnum;
import com.bc.iap.payment.enums.PaymentSubTypeEnum;
import com.bc.iap.payment.enums.PaymentTypeEnum;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class PaymentInfoDto {
    /**
     * 用户唯一标识
     */
    private Long uid;
    /**
     * umk产品唯一标识
     */
    private Long appId;

    /**
     * 付款金额，精确到小数点2位
     */
    private BigDecimal amount;

    /**
     * 剧集ID
     */
    private Long dramaId;
    /**
     * 备注
     */
    private String remarks;

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 订单类型
     */
    private Integer orderType;
    /**
     * 用户Ip
     */
    private String ip;

    /**
     * 国家对应枚举
     */
    private CountryEnum countryEnum;

    /**
     * 代理支付平台
     */
    private PaymentTypeEnum proxyPayTypeEnum;

    /**
     * 成功页
     */
    private String successUrl;

    /**
     * sdk 会话key
     */
    private String sessionKey;
    /**
     * 前端付款token
     */
    private String paymentToken;

    /**
     * 具体付款方式
     */
    private PaymentSubTypeEnum subTypeEnum;

    /**
     * 是否ios产品
     */
    private boolean isIos;

    /**
     * 代付版本，收银台 or SDK
     */
    private String proxyPayVer;

    /**
     * 名称
     */
    private String firstName;
    /**
     * 姓氏
     */
    private String lastName;
    /**
     * 邮箱
     */
    private String userEmail;

    /**
     * 接收加密卡信息
     */
    private String encryptedCardInfo;

    /**
     * 统计记录，是否风控、是否快捷支付等
     */
    private PaymentStatDto statDto;

    /**
     * 订阅信息
     */
    private PaymentSubscriptionDto subscriptionDto;

}
