package com.bc.iap.payment.service.pay;

import com.alibaba.fastjson.JSON;
import com.bc.iap.common.utils.DateUtils;
import com.bc.iap.common.utils.SnowFlake;
import com.bc.iap.common.utils.SnowFlake;
import com.bc.iap.payment.model.req.PaypalPayReq;
import com.bc.iap.payment.config.PayPalProperties;
import com.bc.iap.payment.constant.PaymentConstant;
import com.bc.iap.payment.enums.CountryEnum;
import com.bc.iap.payment.enums.PaymentStatusEnum;
import com.bc.iap.payment.enums.PaymentTypeEnum;
import com.bc.iap.payment.model.dto.PaymentCallbackInfoDto;
import com.bc.iap.payment.model.dto.PaymentInfoDto;
import com.bc.iap.payment.model.dto.PaymentOrderStatusInfoDto;
import com.bc.iap.payment.model.dto.QueryOrderStatusDto;
import com.bc.iap.payment.model.req.PaypalPayReq;
import com.bc.iap.payment.model.resp.*;
import com.bc.iap.payment.service.PaymentRestTemplateService;
import com.bc.iap.payment.service.PaymentService;
import com.google.common.collect.ImmutableMap;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.Base64Utils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.time.format.DateTimeFormatter;
import java.util.*;

@RequiredArgsConstructor
@Slf4j
@Component
public class PayPalPaymentServiceImpl implements PaymentService {

    private final PayPalProperties payPalProperties;
    private final PaymentRestTemplateService restTemplate;


    @Override
    public PaymentTypeEnum getPaymentType() {
        return PaymentTypeEnum.PAYPAL;
    }

    public static final List<CountryEnum> countryEnums = Arrays.asList(CountryEnum.values());

    @Override
    public List<CountryEnum> supportCountry() {
        return countryEnums;
    }


    @Override
    public PaymentRspDto launchPayment(PaymentInfoDto infoDto) {
        String token = generateToken();
        if (Strings.isBlank(token)) {
            return null;
        }
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + token);
        headers.set("PayPal-Request-Id", String.valueOf(SnowFlake.getInstance().nextId()));
        PaypalPayReq proxyPayReq = new PaypalPayReq();
        proxyPayReq.setIntent("CAPTURE");
        PaypalPayReq.PurchaseUnitsDTO purchaseUnitsDTO = new PaypalPayReq.PurchaseUnitsDTO();
        purchaseUnitsDTO.setInvoiceId(infoDto.getOrderId());
        PaypalPayReq.AmountDTO amountDTO = new PaypalPayReq.AmountDTO();
        amountDTO.setCurrencyCode(infoDto.getCountryEnum().getCurrencyCode());
        amountDTO.setValue(infoDto.getAmount().setScale(2, RoundingMode.HALF_UP).toPlainString());
        purchaseUnitsDTO.setAmount(amountDTO);
        proxyPayReq.setPurchaseUnits(Collections.singletonList(purchaseUnitsDTO));
        proxyPayReq.setPaymentSource(ImmutableMap.of(infoDto.getSubTypeEnum().getSubType().toLowerCase(), new PaypalPayReq.PaymentSource()));

        HttpEntity<String> entity = new HttpEntity<>(JSON.toJSONString(proxyPayReq), headers);
        log.info("paypal参数:{}", JSON.toJSONString(proxyPayReq));
        String payApiUrl = payPalProperties.getApiUrl() + PaymentConstant.PAYPAL_REQ_ORDER_URL;
        ResponseEntity<PayPalProxyPayResp> resp = restTemplate.postForEntity(payApiUrl, entity, PayPalProxyPayResp.class);
        if (!resp.getStatusCode().is2xxSuccessful() || resp.getBody() == null
                || !Arrays.asList(PaymentConstant.PaypalStatus.CREATED, PaymentConstant.PaypalStatus.WAIT_PAY).contains(resp.getBody().getStatus())) {
            log.warn("paypal收款订单提交失败!result:{}", JSON.toJSONString(resp.getBody()));
            return null;
        }
        PayPalProxyPayResp payMaxProxyPayResp = resp.getBody();
        PaymentRspDto paymentRspDto = new PaymentRspDto();
        paymentRspDto.setMerchantId(payPalProperties.getPaypalClientId());
//        String redirectUrl = proxyPayDomainService.getProxyPayDomainList().get(0) + String.format(redirectPath, payMaxProxyPayResp.getId(), infoDto.getAmount(),
//                payPalProperties.getPaypalClientId(), infoDto.getCountryEnum().getCurrencyCode(), ProxyPayChannelSdkEnum.PAYPAL.getCid());
//        paymentRspDto.setRedirectUrl(redirectUrl);
        paymentRspDto.setOutTradeNo(payMaxProxyPayResp.getId());
        return paymentRspDto;
    }

    public String generateToken() {
        MultiValueMap<String, Object> formData = new LinkedMultiValueMap<>();
        formData.add("grant_type", "client_credentials");
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        String authStr = Base64Utils.encodeToString((payPalProperties.getPaypalClientId() + ":" + payPalProperties.getPaypalClientSecret()).getBytes(StandardCharsets.UTF_8));
        headers.set("Authorization", "Basic " + authStr);
        HttpEntity<MultiValueMap<String, Object>> entity = new HttpEntity<>(formData, headers);
        String payApiUrl = payPalProperties.getApiUrl() + PaymentConstant.PAYPAL_AUTH_URL;
        ResponseEntity<PayPalAuthResp> resp = restTemplate.postForEntity(payApiUrl, entity, PayPalAuthResp.class);
        if (resp.getBody() == null || Strings.isBlank(resp.getBody().getAccessToken())) {
            log.error("生成paypal代付token异常");
            return null;
        }
        return resp.getBody().getAccessToken();
    }


    @Override
    public PaymentOrderStatusInfoDto queryOrderStatus(QueryOrderStatusDto queryOrderDto) {
        String token = generateToken();
        if (Strings.isBlank(token)) {
            return null;
        }
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + token);
        headers.set("PayPal-Request-Id", UUID.randomUUID().toString());
        HttpEntity<String> entity = new HttpEntity<>(null, headers);
        String payApiUrl = payPalProperties.getApiUrl() + String.format(PaymentConstant.PAYPAL_QUERY_ORDER_URL, queryOrderDto.getOutTradeNo());
        ResponseEntity<PayPalOrderStatusResp> resp = restTemplate.getForEntityErrReturn(payApiUrl, HttpMethod.GET, entity, PayPalOrderStatusResp.class);
        PayPalOrderStatusResp statusResp = resp.getBody();
        if (statusResp == null) {
            return null;
        }
        PaymentOrderStatusInfoDto statusInfoDto = new PaymentOrderStatusInfoDto();
        if (!resp.getStatusCode().is2xxSuccessful()) {
            statusInfoDto.setStatusEnum(PaymentStatusEnum.FAIL_PAY);
            if (CollectionUtils.isNotEmpty(statusResp.getDetails()) && statusResp.getDetails().get(0) != null) {
                String description = statusResp.getDetails().get(0).getDescription();
                statusInfoDto.setMessage(description.length() > 200 ? description.substring(0, 200) : description);
                statusInfoDto.setStatus(statusResp.getDetails().get(0).getIssue());
            }
            return statusInfoDto;
        }
        statusInfoDto.setStatusEnum(PaymentConstant.PaypalStatus.COMPLETED.equals(statusResp.getStatus()) ? PaymentStatusEnum.SUCCESS_PAY :
                Arrays.asList(PaymentConstant.PaypalStatus.WAIT_PAY, PaymentConstant.PaypalStatus.CREATED).contains(statusResp.getStatus()) ? PaymentStatusEnum.WAIT_PAY
                        : PaymentStatusEnum.FAIL_PAY);
        if (statusInfoDto.getStatusEnum() != PaymentStatusEnum.WAIT_PAY && statusResp.getUpdateTime() != null) {
            statusInfoDto.setFinishTime(DateUtils.parseDate(statusResp.getUpdateTime(), DateUtils.FMT_DATE_TIME));
        }
        if (CollectionUtils.isNotEmpty(statusResp.getPurchaseUnits())) {
            statusInfoDto.setAmount(new BigDecimal(statusResp.getPurchaseUnits().get(0).getAmount().getValue()));
        }
//        if (MapUtils.isNotEmpty(statusResp.getPaymentSource())) {
//            statusInfoDto.setPaymentMethodType(statusResp.getPaymentSource());
//        }
//        if (statusInfoDto.getFees() != null && resultDataDTO.getFees().getMerFee() != null) {
//            statusInfoDto.setFee(new BigDecimal(resultDataDTO.getFees().getMerFee().getAmount()));
//            statusInfoDto.setFeeCurrency(resultDataDTO.getFees().getMerFee().getCurrency());
//        }
        return statusInfoDto;
    }

    @Override
    public PaymentCallbackInfoDto paymentCallback(Map<String, Object> paramMap, HttpServletRequest request) {
        log.info("paypal支付回调:{}", JSON.toJSONString(paramMap));
        if (paramMap.get("orderTradeId") == null) {
            return null;
        }
        String token = generateToken();
        if (Strings.isBlank(token)) {
            return null;
        }
        String orderTradeId = String.valueOf(paramMap.get("orderTradeId"));
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", "Bearer " + token);
        headers.set("PayPal-Request-Id", UUID.randomUUID().toString());
        HttpEntity<String> entity = new HttpEntity<>(null, headers);
        String payApiUrl = payPalProperties.getApiUrl() + String.format(PaymentConstant.PAYPAL_CAPTURE_ORDER_URL, orderTradeId);
        ResponseEntity<PaypalCaptureResp> resp = restTemplate.postForEntityErrReturn(payApiUrl, entity, PaypalCaptureResp.class);
        PaypalCaptureResp captureResp = resp.getBody();
        if (captureResp == null) {
            return null;
        }
        PaymentCallbackInfoDto callbackInfoDto = new PaymentCallbackInfoDto();
        callbackInfoDto.setOrderTradeNo(orderTradeId);
        if (!resp.getStatusCode().is2xxSuccessful()) {
            callbackInfoDto.setStatusEnum(PaymentStatusEnum.FAIL_PAY);
            String description = captureResp.getDetails().get(0).getDescription();
            callbackInfoDto.setMessage(description.length() > 200 ? description.substring(0, 200) : description);
            callbackInfoDto.setStatus(captureResp.getDetails().get(0).getIssue());
            return callbackInfoDto;
        }
        log.info("paypal捕获订单信息:{}", JSON.toJSONString(captureResp));
        PaypalCaptureResp.PurchaseUnitsDTO purchaseUnitsDTO = captureResp.getPurchaseUnits().get(0);
        PaypalCaptureResp.CapturesDTO capturesDTO = purchaseUnitsDTO.getPayments().getCaptures().get(0);
        callbackInfoDto.setMerchantId(payPalProperties.getPaypalClientId());
        PaymentStatusEnum statusEnum = PaymentConstant.PaypalStatus.COMPLETED.equals(capturesDTO.getStatus())
                ? PaymentStatusEnum.SUCCESS_PAY : PaymentStatusEnum.FAIL_PAY;
        callbackInfoDto.setStatusEnum(statusEnum);
        callbackInfoDto.setStatus(captureResp.getStatus());
        callbackInfoDto.setRealAmount(new BigDecimal(capturesDTO.getAmount().getValue()));
        callbackInfoDto.setFinishTime(DateUtils.parseDate(capturesDTO.getUpdateTime(), DateTimeFormatter.ISO_ZONED_DATE_TIME));
        String transType = captureResp.getPaymentSource().getCard() != null ? "card" : "paypal";
        if (statusEnum == PaymentStatusEnum.SUCCESS_PAY) {
            callbackInfoDto.setTransactionType(transType);
            callbackInfoDto.setMerFee(new BigDecimal(capturesDTO.getSellerReceivableBreakdown().getPaypalFee().getValue()));
            callbackInfoDto.setMerFeeCurrency(capturesDTO.getSellerReceivableBreakdown().getPaypalFee().getCurrencyCode());
            captureResp.getPayer().setCaptureId(capturesDTO.getId());
            callbackInfoDto.setPaymentOtherInfo(JSON.toJSONString(captureResp.getPayer()));
        }
        return callbackInfoDto;
    }

}
