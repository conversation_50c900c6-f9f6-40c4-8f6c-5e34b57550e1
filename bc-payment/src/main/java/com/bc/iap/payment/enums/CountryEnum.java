package com.bc.iap.payment.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 国家枚举
 *
 * <AUTHOR>
 * @date 2021/11/10
 */
@Getter
@AllArgsConstructor
public enum CountryEnum {
    //
    INDONESIA("印度尼西亚", "ID", "IDR", "Rp"),
    BRAZIL("巴西", "BR", "BRL", "R$"),
    US("美国", "US", "USD", "$"),
    UK("英国", "UK", "GBP", "£"),

    DE("德国", "DE", "EUR", "€"),
    CA("加拿大", "CA", "CAD", "C$"),
    AU("澳大利亚", "AU", "AUD", "A$"),
    MALAYSIA("马来西亚", "MY", "MYR", "RM"),
    THAILAND("泰国", "TH", "THB", "฿"),
    FR("法国", "FR", "EUR", "€"),
    MX("墨西哥", "MX", "MXN", "M$"),
    IN("印度", "IN", "INR", "₹"),
    JP("日本", "JP", "JPY", "¥"),
    PH("菲律宾", "PH", "PHP", "₱"),
    SG("新加坡", "SG", "SGD", "S$"),
    KR("韩国", "KR", "KRW", "₩"),
    RU("俄罗斯", "RU", "RUB", "₽"),

    NZ("新西兰", "NZ", "NZD", "NZ$"),
    CO("哥伦比亚", "CO", "COP", "COL$"),
    CL("智利", "CL", "CLP", "CLP$"),
    PO("波兰", "PO", "PLN", "zł"),
    IT("意大利", "IT", "ITL", "₤"),
    SE("瑞典", "SE", "SEK", "kr"),
    NO("挪威", "NO", "NOK", "kr"),
    MY("马来西亚", "MY", "MYR", "M.＄"),
    DK("丹麦", "DK", "DKK", "kr"),
    FI("芬兰", "FI", "EUR", "€"),
    TR("土耳其", "TR", "TRY", "₺"),
    CH("瑞士", "CH", "CHF", "₣"),
    BE("比利时", "BE", "EUR", "€"),
    IE("爱尔兰", "IE", "IEP", "IR£"),
    NL("荷兰", "NL", "ANG", "ƒ"),
    AR("阿根廷", "AR", "ARS", "ARS$"),
    ES("西班牙", "ES", "EUR", "€"),
    TH("泰国", "TH", "THB", "฿"),
    EG("埃及", "EG", "EGP", "E£"),
    HK("香港", "HK", "HKD", "HK$"),
    TW("台湾", "TW", "NTD", "NT$"),

    ;

    private String name;
    private String countryCode;
    private String currencyCode;
    private String symbol;

    public static CountryEnum getCountryEnum(String countryCode) {
        for (CountryEnum countryEnum : CountryEnum.values()) {
            if (countryEnum.getCountryCode().equalsIgnoreCase(countryCode)) {
                return countryEnum;
            }
        }
        return null;
    }
}
