package com.bc.iap.payment.service;

import com.bc.iap.payment.mapper.TDramaComboInfoMapper;
import com.bc.iap.payment.model.entity.TDramaOrder;
import com.bc.iap.user.mapper.TUserAssetInfoMapper;
import com.bc.iap.user.mapper.TUserMapper;
import com.bc.iap.user.model.entity.TUserAssetInfo;
import com.bc.iap.user.model.enums.ComboTypeEnum;
import com.zz.common.util.CollectionUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Component
@RequiredArgsConstructor
@Slf4j
public class AssetService {
    private final TUserAssetInfoMapper userAssetInfoMapper;
    private final TDramaComboInfoMapper dramaComboInfoMapper;
    private final TUserMapper userMapper;


    /**
     * 更新用户资产信息
     */
    public void updateUserAssetInfo(TDramaOrder order) {
        TUserAssetInfo assetInfo = new TUserAssetInfo();
        assetInfo.setOrderId(order.getOrderId());
        assetInfo.setAppId(order.getAppId());
        assetInfo.setUserId(order.getUserId());
        assetInfo.setOrderType(order.getOrderType());
        assetInfo.setDramaId(order.getDramaId());
        if (order.getDramaId() > 0) {
            assetInfo.setActiveTime(LocalDateTime.now());
            assetInfo.setExpireTime(LocalDateTime.now().plusYears(99));
            int count = userAssetInfoMapper.insert(assetInfo);
            log.info("支付成功，更新用户剧集资产!count:{}", count);
        } else {
            List<TUserAssetInfo> userAssetInfos = userAssetInfoMapper.getUserAssetList(order.getUserId());
            ComboTypeEnum typeEnum = ComboTypeEnum.getComboType(order.getOrderType());
            if (!CollectionUtils.isEmpty(userAssetInfos)) {
                Optional<TUserAssetInfo> cardAssetOptional = userAssetInfos.stream().filter(x -> x.getOrderType() != 0).findFirst();
                if (cardAssetOptional.isPresent()) {
                    TUserAssetInfo cardAssetInfo = cardAssetOptional.get();
                    if (cardAssetInfo.getExpireTime().isBefore(LocalDateTime.now())) {
                        cardAssetInfo.setActiveTime(LocalDateTime.now());
                        cardAssetInfo.setExpireTime(LocalDateTime.now().plusDays(typeEnum.getDay()));
                    } else {
                        cardAssetInfo.setExpireTime(cardAssetInfo.getExpireTime().plusDays(typeEnum.getDay()));
                    }
                    int count = userAssetInfoMapper.updateById(cardAssetInfo);
                    log.info("支付成功，更新用户激活资产!count:{}", count);
                }
            } else {
                assetInfo.setActiveTime(LocalDateTime.now());
                assetInfo.setExpireTime(LocalDateTime.now().plusDays(typeEnum.getDay()));
                int count = userAssetInfoMapper.insert(assetInfo);
                log.info("支付成功，插入用户资产!count:{}", count);
            }
        }
    }
}
