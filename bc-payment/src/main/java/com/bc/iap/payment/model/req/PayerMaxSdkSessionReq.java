package com.bc.iap.payment.model.req;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class PayerMaxSdkSessionReq extends PayerMaxPayBaseReq {


    @JSONField(name = "data")
    private DataDTO data;

    @NoArgsConstructor
    @Data
    public static class DataDTO {
        @JSONField(name = "currency")
        private String currency;
        @JSONField(name = "country")
        private String country;
        @JSONField(name = "userId")
        private String userId;
        @JSONField(name = "totalAmount")
        private String totalAmount;
        @JSONField(name = "componentList")
        private List<String> componentList;
    }
}
