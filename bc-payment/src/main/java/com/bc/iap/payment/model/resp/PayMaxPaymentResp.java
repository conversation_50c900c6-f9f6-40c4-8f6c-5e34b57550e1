package com.bc.iap.payment.model.resp;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * Payment pay max pay resp
 * Created in 2024.07.08
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@Data
@Accessors(chain = true)
public class PayMaxPaymentResp {

    @JSONField(name = "code")
    private String code;
    @JSONField(name = "msg")
    private String msg;
    @JSONField(name = "data")
    private DataDTO data;

    /**
     * Data dto
     * Created in 2024.07.08
     *
     * <AUTHOR>
     */
    @NoArgsConstructor
    @Data
    public static class DataDTO {
        @JSONField(name = "redirectUrl")
        private String redirectUrl;
        @JSONField(name = "outTradeNo")
        private String outTradeNo;
        @J<PERSON><PERSON>ield(name = "tradeToken")
        private String tradeToken;
        @JSONField(name = "status")
        private String status;
    }
}
