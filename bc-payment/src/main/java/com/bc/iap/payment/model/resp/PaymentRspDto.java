package com.bc.iap.payment.model.resp;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class PaymentRspDto {
    @JSONField(name = "merchantId")
    private String merchantId;
    @JSONField(name = "redirectUrl")
    private String redirectUrl;
    @JSONField(name = "outTradeNo")
    private String outTradeNo;
    @JSONField(name = "status")
    private String status;
    @JSONField(name = "message")
    private String message;
    @JSONField(name = "success")
    private Boolean isSuccess = true;

}
