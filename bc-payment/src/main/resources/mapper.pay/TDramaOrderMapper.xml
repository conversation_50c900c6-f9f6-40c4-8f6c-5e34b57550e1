<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bc.iap.payment.mapper.TDramaOrderMapper">
    <update id="updateOrderInfo" parameterType="com.bc.iap.payment.model.entity.TDramaOrder">
        update t_drama_order set pay_status=#{payStatus}
        <if test="merchantId!=null">
            ,merchant_id=#{merchantId}
        </if>
        <if test="payPlatform!=null">
            ,pay_platform=#{payPlatform}
        </if>
        <if test="outTradeNo!=null">
            ,out_trade_no=#{outTradeNo}
        </if>
        <if test="realAmount!=null">
            ,real_amount=#{realAmount}
        </if>
        <if test="usdValue!=null">
            ,usd_value=#{usdValue}
        </if>
        <if test="orderFinishTime!=null">
            ,order_finish_time=#{orderFinishTime}
        </if>
        <if test="orderMessage!=null">
            ,order_message=#{orderMessage}
        </if>
        <if test="paymentMethodType!=null">
            ,payment_method_type=#{paymentMethodType}
        </if>
        <if test="paymentOtherInfo!=null">
            ,payment_other_info=#{paymentOtherInfo}
        </if>
        <if test="merFee!=null">
            ,mer_fee=#{merFee}
        </if>
        <if test="merFeeCurrency!=null">
            ,mer_fee_currency=#{merFeeCurrency}
        </if>
        where order_id=#{orderId}
    </update>
</mapper>
