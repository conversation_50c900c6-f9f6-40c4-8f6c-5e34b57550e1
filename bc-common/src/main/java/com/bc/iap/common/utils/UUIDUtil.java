package com.bc.iap.common.utils;

import org.apache.commons.codec.binary.Base64;

import java.nio.ByteBuffer;
import java.util.UUID;

public class UUIDUtil {
    /**
     * <p>Title: getKeys</p>
     * <p>Description:获取随机生成的32位主键 </p>
     */
    public static String getKeys() {
        String uuid = UUID.randomUUID().toString();
        return uuid.replace("-", "");
    }


    /**
     * 利用Base64编码, 生成22位UUID
     */
    @SuppressWarnings("unused")
    public static String uuidBase64() {
        return toBase64(UUID.randomUUID());
    }


    public static String toBase64(UUID uuid) {
        ByteBuffer bb = ByteBuffer.wrap(new byte[16]);
        bb.putLong(uuid.getMostSignificantBits());
        bb.putLong(uuid.getLeastSignificantBits());
        return Base64.encodeBase64URLSafeString(bb.array());
    }

    @SuppressWarnings("unused")
    public static String fromBase64(String str) {
        byte[] bytes = Base64.decodeBase64(str);
        ByteBuffer bb = ByteBuffer.wrap(bytes);
        UUID uuid = new UUID(bb.getLong(), bb.getLong());
        return uuid.toString();
    }

}
