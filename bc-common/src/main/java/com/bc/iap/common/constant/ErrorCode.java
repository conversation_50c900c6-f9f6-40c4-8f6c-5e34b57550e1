package com.bc.iap.common.constant;


/**  
* <p>Title: ResultCode</p>  
* <p>Description: 返回的错误代码</p>  
* <AUTHOR>  
* @date 2018年8月9日  
*/  
public class ErrorCode {

    /** 成功 */
    public static final int SUCCESS = 0;

    /** 失败 */
    public static final int FAILURE = -1;

    /** 参数错误 */
    public static final int PARAMETER_ERROR = 1;

    /** 数据库错误 */
    public static final int DB_ERROR = 2;

    /** 未登录 */
    public static final int NOT_LOGIN = 3;

    /** 用户不存在 **/
    public static final int USER_NOT_EXIST = 4;

    /** 无权限访问**/
    public static final int NOT_ACCESS = 5;

    /**用户非VIP**/
    public static final int USER_NOT_VIP = 7;

    /** 内部服务器错误 */
    public static final int INTERNAL_SERVER_ERROR = 500;
    
    /** 没有此纪录 */
    public static final int NOT_RECORD = 6;

    /**
     * 重复请求
     * */
    public static final int DUPLICATE_REQUEST = 8;

    /**
     * 黑名单用户
     */
    public static final int BLACK_LIST_USER = 9;

}
