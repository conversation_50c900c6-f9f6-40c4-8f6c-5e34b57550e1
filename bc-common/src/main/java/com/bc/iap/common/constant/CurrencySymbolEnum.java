package com.bc.iap.common.constant;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 货币符号 枚举类
 */
public enum CurrencySymbolEnum {
	CNY("CNY","¥"),
	USD("USD","$");
	@Getter
	String symbol;
	@Getter
	String currency;

	CurrencySymbolEnum(String currency, String symbol) {
		this.symbol = symbol;
		this.currency = currency;
	}

	public static String getSymbol(String currency){
		for (CurrencySymbolEnum val: values()){
			if(StringUtils.equals(val.currency, currency)){
				return val.symbol;
			}
		}
		return currency;
	}

	public static boolean isDollar(String currency){
		return StringUtils.equals(USD.currency, currency);
	}
}
