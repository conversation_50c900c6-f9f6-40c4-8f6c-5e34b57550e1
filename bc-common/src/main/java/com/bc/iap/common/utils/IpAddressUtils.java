package com.bc.iap.common.utils;

import com.zz.common.util.IPUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import sun.net.util.IPAddressUtil;

import javax.servlet.http.HttpServletRequest;
import java.net.*;
import java.util.Enumeration;
import java.util.Optional;
import java.util.regex.Pattern;

/**
 * <br>
 * IP工具类
 */
@Slf4j
public final class IpAddressUtils {

	private IpAddressUtils() {
	}

	private static final Pattern IP_PATTERN = Pattern.compile("^[0-9]{1,3}.[0-9]{1,3}.[0-9]{1,3}.[0-9]{1,3}$");
	private static final Pattern PRIVATE_IP_PATTERN = Pattern.compile("127\\.0\\.0\\.1");
	private static final String UNKNOWN = "unknown";
	// private static String localIp = doGetLocalIp();// 线上机器是将两个网卡绑定在一起的，故这种方式获取到的是127IP
	private static String localIp = getLocalIPMethod(true);

	/**
	 * 获取本机IP地址
	 */
	public static String getLocalIp() {
		return localIp;
	}

	/**
	 * 判断是否是一个IP地址格式
	 */
	public static boolean isIP(String ip) {
		return StringUtils.isNotBlank(ip) && IP_PATTERN.matcher(ip).matches();
	}

	/**
	 * 判断是否是一个回环IP
	 */
	public static boolean isLoopbackIP(String ip) {
		return StringUtils.isNotBlank(ip) && PRIVATE_IP_PATTERN.matcher(ip).matches();
	}

	/**
	 * 判断是否是本地IP
	 */
	public static boolean isLocalIP(String ip) {
		return isLoopbackIP(ip) || localIp.equals(ip);
	}

	// ####
	// ## private func
	// ####

	protected static String doGetLocalIp() {
		String ip = null;
		try {
			String os = System.getProperty("os.name").toLowerCase();
			if (os.startsWith("windows")) {
				InetAddress localHost = InetAddress.getLocalHost();
				ip = localHost.getHostAddress();
			}
			// Linux
			else {
				ip = getLinuxIpAddress();
			}
		} catch (UnknownHostException e) {
			log.error(e.getMessage(), e);
		}
		log.info("The LocalIpAddress (1) Is {}", ip);
		return ip;
	}

	private static String getLinuxIpAddress() {
		String ip = null;
		try {
			Enumeration<NetworkInterface> interfaces = (Enumeration<NetworkInterface>) NetworkInterface.getNetworkInterfaces();
			while (interfaces.hasMoreElements()) {
				NetworkInterface ni = (NetworkInterface) interfaces.nextElement();
				Enumeration<InetAddress> addresses = ni.getInetAddresses();
				while (addresses.hasMoreElements()) {
					InetAddress address = (InetAddress) addresses.nextElement();
					if (address instanceof Inet6Address)
						continue;
					ip = address.getHostAddress();
					if (null == ip || !isIP(ip) || !isLoopbackIP(ip))
						continue;
					return ip;
				}

			}
		} catch (SocketException e) {
			log.error(e.getMessage(), e);
		}
		return ip;
	}

	// ####################################

	/**
	 * 将IP地址转换成InetAddress，系统底层不会产生域名服务调用
	 * 
	 * @param rawIP IP地址的串，比如"************"
	 * @return null if rawIP error
	 */
	public static InetAddress ip2Addr(String rawIP) {
		if(StringUtils.isBlank(rawIP)){
			return null;
		}
		byte[] addr = text2Numeric(rawIP);
		try {
			return InetAddress.getByAddress(rawIP, addr);
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	public static byte[] text2Numeric(String paramString) {


		byte[] arrayOfByte = new byte[4];
		String[] arrayOfString = StringUtils.split(paramString, ".");
		try {
			long l;
			int i;
			switch (arrayOfString.length) {
				case 1:
					l = Long.parseLong(arrayOfString[0]);
					if ((l < 0L) || (l > 4294967295L)){
						return new byte[0];
					}
					arrayOfByte[0] = (byte) (int) (l >> 24 & 0xFF);
					arrayOfByte[1] = (byte) (int) ((l & 0xFFFFFF) >> 16 & 0xFF);
					arrayOfByte[2] = (byte) (int) ((l & 0xFFFF) >> 8 & 0xFF);
					arrayOfByte[3] = (byte) (int) (l & 0xFF);
					break;
				case 2:
					l = Integer.parseInt(arrayOfString[0]);
					if ((l < 0L) || (l > 255L)){
						return new byte[0];
					}

					arrayOfByte[0] = (byte) (int) (l & 0xFF);
					l = Integer.parseInt(arrayOfString[1]);
					if ((l < 0L) || (l > 16777215L))
						return new byte[0];
					arrayOfByte[1] = (byte) (int) (l >> 16 & 0xFF);
					arrayOfByte[2] = (byte) (int) ((l & 0xFFFF) >> 8 & 0xFF);
					arrayOfByte[3] = (byte) (int) (l & 0xFF);
					break;
				case 3:
					for (i = 0; i < 2; ++i) {
						l = Integer.parseInt(arrayOfString[i]);
						if ((l < 0L) || (l > 255L))
							return new byte[0];
						arrayOfByte[i] = (byte) (int) (l & 0xFF);
					}
					l = Integer.parseInt(arrayOfString[2]);
					if ((l < 0L) || (l > 65535L))
						return new byte[0];
					arrayOfByte[2] = (byte) (int) (l >> 8 & 0xFF);
					arrayOfByte[3] = (byte) (int) (l & 0xFF);
					break;
				case 4:
					for (i = 0; i < 4; ++i) {
						l = Integer.parseInt(arrayOfString[i]);
						if ((l < 0L) || (l > 255L)){
							return new byte[0];
						}
						arrayOfByte[i] = (byte) (int) (l & 0xFF);
					}
					break;
				default:
					return new byte[0];
			}
		} catch (NumberFormatException localNumberFormatException) {
			return new byte[0];
		}
		return arrayOfByte;
	}

	public static long textToLongFormatV4(String paramString) {
		if(StringUtils.isBlank(paramString)){
			return -1;
		}
		byte[] ip = text2Numeric(paramString);
		if (ip.length == 0) {
			return -1;
		} else {
			return ((ip[0] << 24) & 0xFFFFFFFFL) + ((ip[1] << 16) & 0xFFFFFF) + ((ip[2] << 8) & 0xFFFF) + (ip[3] & 0xFF);
		}
	}

	public static boolean isIPv4LiteralAddress(String paramString) {
		if(StringUtils.isBlank(paramString)){
			return false;
		}
		return (text2Numeric(paramString).length>0);
	}

	private static String getLocalIPMethod(boolean innerNet) {
		String ip = doGetLocalIPMethod(innerNet);
		log.info("The LocalIpAddress (2) Is {}", ip);
		return ip;
	}

	/**
	 * 获取本机的内网或外网IP(第一个匹配马上返回)
	 * 
	 * @param innerNet true:内网IP false:外网IP
	 * @return
	 */
	private static String doGetLocalIPMethod(boolean innerNet) {
		try {
			Enumeration<NetworkInterface> netInterfaces = NetworkInterface.getNetworkInterfaces();
			while (netInterfaces.hasMoreElements()) {
				NetworkInterface ni = netInterfaces.nextElement();
				Enumeration<InetAddress> address = ni.getInetAddresses();
				while (address.hasMoreElements()) {
					InetAddress ip = address.nextElement();
					String ipStr = ip.getHostAddress();
					if (ip.isAnyLocalAddress() //
							|| ip.isLinkLocalAddress() //
							|| ip.isLoopbackAddress() //
							|| ip.isMCNodeLocal() //
							|| ip.isMCLinkLocal() //
							|| ip.isMCOrgLocal() //
							|| ip.isMCSiteLocal() //
							|| ip.isMulticastAddress() //
							|| ipStr.contains(":")) {// 以上都是：先把特殊的IP过滤掉

					} else if (ip.isSiteLocalAddress()) {// 内网IP
						if (innerNet)
							return ipStr;
					} else {// 外网IP
						return ipStr;
					}
				}
			}
		} catch (SocketException e) {
			return null;
		}
		return null;
	}

	// ipv4 的格式
	private static final Pattern IPV4_PATTERN = Pattern.compile("^(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)(\\.(25[0-5]|2[0-4]\\d|[0-1]?\\d?\\d)){3}$");

	/**
	 *
	 * 获取正确的客户端ip地址
	 *
	 * @param request HttpServletRequest
	 * @return 返回客户端的IP
	 */
	public static String getClientIpAddr(HttpServletRequest request) {
		String ip = "";
		if (request != null) {
			ip = request.getHeader("x-forwarded-for");
			if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
				ip = request.getHeader("Proxy-Client-IP");
			}
			if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
				ip = request.getHeader("WL-Proxy-Client-IP");
			}
			if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
				ip = request.getRemoteAddr();
			}
		}
		if (ip != null && ip.contains(",")) {
			ip = ip.split(",")[0].replaceAll(" ", "");
		}
		return ip;
	}

	
	/**
	 * 测试传入的参数是否正确的ipv4格式
	 *
	 * @param ip
	 * @return true=是IPV4
	 */
	public static boolean isIPv4Address(String ip) {
		if (ip == null)
			return false;

		return IPV4_PATTERN.matcher(ip).matches();
	}


	public static long inetAton(String strIp){
		strIp = Optional.ofNullable(strIp).orElse("127.0.0.1");
		String[] ip = strIp.split("\\.");
		return (Long.parseLong(ip[0]) << 24)+(Long.parseLong(ip[1])<<16)+(Long.parseLong(ip[2])<<8)+ Long.parseLong(ip[3]);

	}


	public static String inetNtoa(long longIp){
		StringBuffer sb = new StringBuffer("");
		// 直接右移24位
		sb.append(String.valueOf((longIp >>> 24)));
		sb.append(".");
		// 将高8位置0，然后右移16位
		sb.append(String.valueOf((longIp & 0x00FFFFFF) >>> 16));
		sb.append(".");
		// 将高16位置0，然后右移8位
		sb.append(String.valueOf((longIp & 0x0000FFFF) >>> 8));
		sb.append(".");
		// 将高24位置0
		sb.append(String.valueOf((longIp & 0x000000FF)));
		return sb.toString();
	}



	public static boolean isVpn(String ip){
		return IPUtils.isVPN(ip);
	}



	/**
	 * 判断是否内网IP
	 * @param ip
	 * @return
	 */
	public static boolean isInternalIp(String ip) {
		byte[] addr = IPAddressUtil.textToNumericFormatV4(ip);
		return internalIp(addr);
	}

	private static boolean internalIp(byte[] addr) {
		final byte b0 = addr[0];
		final byte b1 = addr[1];
		//10.x.x.x/8
		final byte SECTION_1 = 0x0A;
		//172.16.x.x/12
		final byte SECTION_2 = (byte) 0xAC;
		final byte SECTION_3 = (byte) 0x10;
		final byte SECTION_4 = (byte) 0x1F;
		//192.168.x.x/16
		final byte SECTION_5 = (byte) 0xC0;
		final byte SECTION_6 = (byte) 0xA8;
		switch (b0) {
			case SECTION_1:
				return true;
			case SECTION_2:
				if (b1 >= SECTION_3 && b1 <= SECTION_4) {
					return true;
				}
			case SECTION_5:
				switch (b1) {
					case SECTION_6:
						return true;
				}
			default:
				return false;
		}
	}


}
