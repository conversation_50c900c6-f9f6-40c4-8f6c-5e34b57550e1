package com.bc.iap.common.utils;

import com.bc.iap.common.constant.GlobalConstant;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.ServletRequest;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;

/**
 * Request url builder
 * <p/>
 * Created in 2018.12.30
 * <p/>
 *
 * <AUTHOR>
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@Slf4j
public class RequestUtils {

    /**
     * 构造请求参数.
     *
     * @param params the 参数
     * @return the string 转化后的Query查询参数
     */
    public static String buildQuery(Map<String, String> params) {
        if (params == null || params.isEmpty()) {
            return "";
        }
        StringBuilder query = new StringBuilder();
        boolean hasParams = false;
        try {
            for (Map.Entry<String, String> entry : params.entrySet()) {
                if (!Strings.isNullOrEmpty(entry.getKey())) {
                    if (hasParams) {
                        query.append(GlobalConstant.Symbol.EG);
                    } else {
                        hasParams = true;
                    }
                    query.append(entry.getKey()).append("=").append(URLEncoder.encode(
                            entry.getValue(), "UTF-8"));
                }
            }
        } catch (UnsupportedEncodingException e) {
            log.error("拼接query参数异常!errMsg={}", e.getMessage());
        }
        return query.toString();
    }

    /**
     * 构造完整url.
     *
     * @param url    the url
     * @param params the 参数
     * @return the string 转化后的url
     * @throws IOException the io exception
     */
    public static String buildRequestUrl(String url, Map<String, String> params) {
        if (params == null || params.isEmpty()) {
            return url;
        }
        StringBuilder builder = new StringBuilder();
        // 如果以/结果,去掉/
        if (url.endsWith(GlobalConstant.Symbol.SLASH)) {
            builder.append(url, 0, url.length() - 1);
        } else {
            builder.append(url);
        }
        if (!(url.endsWith(GlobalConstant.Symbol.QM) || url.endsWith(GlobalConstant.Symbol.EG))) {
            if (url.contains(GlobalConstant.Symbol.QM)) {
                builder.append(GlobalConstant.Symbol.EG);
            } else {
                builder.append(GlobalConstant.Symbol.QM);
            }
        }
        return builder.append(buildQuery(params)).toString();
    }

    /**
     * 获取request 中的参数，以map形式返回
     *
     * @param request
     * @return
     */
    public static Map<String, Object> getParamMap(ServletRequest request) {
        //Assert.notNull(request,"参数不能为空");
        Map<String, Object> map = Maps.newHashMap();
        Enumeration<String> en = request.getParameterNames();
        while (en.hasMoreElements()) {
            String name = en.nextElement();
            String[] values = request.getParameterValues(name);
            if (values == null || values.length == 0) {
                continue;
            }
            String value = values[0];
            if (value != null) {
                map.put(name, value);
            }
        }
        return map;
    }

    public static Map<String, Object> getParam(String url) {
        //Assert.notNull(request,"参数不能为空");
        Map<String, Object> map = Maps.newHashMap();
        String query = url.substring(url.indexOf("?") + 1);
        String[] params = query.split("&");
        for (String param : params) {
            String[] kv = param.split("=");
            map.put(kv[0], kv.length == 1 ? "" : kv[1]);
        }
        return map;
    }

    /**
     * 把数组所有元素，按字母排序，然后按照“参数=参数值”的模式用“&”字符拼接成字符串
     *
     * @param params 需要签名的参数
     * @return 签名的字符串
     */
    public static String createLinkString(Map<String, Object> params) {
        List<String> keys = Lists.newArrayList(params.keySet().iterator());
        Collections.sort(keys);
        StringBuilder signStr = new StringBuilder();
        if (CollectionUtils.isEmpty(keys)) {
            return "";
        }
        for (String key : keys) {
//            if (params.get(key) == null || Strings.isNullOrEmpty(params.get(key).toString())) {
//                continue;
//            }
            signStr.append(key).append("=").append(Objects.isNull(params.get(key)) ? "" : params.get(key).toString())
                    .append(GlobalConstant.Symbol.EG);
        }
        return signStr.deleteCharAt(signStr.length() - 1).toString();
    }

    /**
     * 把数组所有元素，按字母排序，然后按照“参数=参数值”的模式用“&”字符拼接成字符串
     *
     * @param params 需要签名的参数
     * @return 签名的字符串
     */
    public static String createUrlLink(Map<String, Object> params) {
        List<String> keys = Lists.newArrayList(params.keySet().iterator());
        Collections.sort(keys);
        StringBuilder signStr = new StringBuilder();
        try {
            for (String key : keys) {
                if (Strings.isNullOrEmpty(params.get(key).toString())) {
                    continue;
                }
                signStr.append(URLEncoder.encode(key, "UTF-8"));
                signStr.append("=");
                if (params.get(key) instanceof String) {
                    String value = (String) params.get(key);
                    signStr.append(URLEncoder.encode(value, "UTF-8"));
                } else {
                    signStr.append(params.get(key));
                }
                signStr.append(GlobalConstant.Symbol.EG);
            }
        } catch (UnsupportedEncodingException e) {
            log.error("拼接query参数异常!errMsg={}", e.getMessage());
        }
        return signStr.deleteCharAt(signStr.length() - 1).toString();
    }

    public static String createKVStr(Map<String, Object> params) {
        List<String> keys = Lists.newArrayList(params.keySet().iterator());
        Collections.sort(keys);
        StringBuilder signStr = new StringBuilder();
        for (String key : keys) {
            signStr.append(key);
            signStr.append("=");
            String value = StringUtils.defaultIfBlank(params.get(key).toString(), "");
            signStr.append(value);
            signStr.append(GlobalConstant.Symbol.COMMA);
        }
        return signStr.deleteCharAt(signStr.length() - 1).toString();
    }
}
