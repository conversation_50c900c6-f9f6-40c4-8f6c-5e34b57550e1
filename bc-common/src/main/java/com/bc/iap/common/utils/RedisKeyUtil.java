package com.bc.iap.common.utils;

import org.apache.commons.lang3.StringUtils;

/**
 * 管理reidis key
 */
public class RedisKeyUtil {
	public static final String SEPERATOR = ":";
	public static final String IMG_CODE_KEY = "img_code_uuid";
	public static final String LOGIN_TOKEN = "bc_login_token";
	public static final String LOGIN_TOKEN_UID = "bc_login_token_uid";
	public static final String LOGIN_TOKEN_APP_ID = "bc_login_token_app_id";
	public static final String USER_TOKEN = "bc_user_token";

	public static final String BLACK_LIST = "black";

	public static final String BLACK_LIST_IP_COUNTRY = BLACK_LIST + SEPERATOR + "country";


	public static String buildBlackListUserIpCountry(Long userId) {
		return BLACK_LIST_IP_COUNTRY + userId;
	}


	public static final String PAYPAL_OAUTH2_TOKEN = "paypal:oauth2token";

	public static final String BAK_PAYPAL_OAUTH2_TOKEN = "paypal:bakoauth2token";

	public static final String PAYPAL_OAUTH2_TOKEN_BY_ACCOUNT = "paypal:oauth2tokenbyaccount";

	public static final String BAK_PAYPAL_SWITCH = "paypal:bakswitch";

	public static final String PAYPAL_FUSE_ERROR = "paypal:fuseerror";
	public static final String BIGDATA_OFFER_CACHE = "bigdata:offercache";

	public static final String BAK_PAYPAL_CLOSE_AUTH = "paypal:bakcloseauth";

	public static final String BAK_PROXY_SWITCH = "proxy:bakswitch";
	public static final String PROXY_FUSE_ERROR = "proxy:fuseerror";

	public static final String BAK_PROXY_PAY_SWITCH = "proxy:pay:bakswitch";
	public static final String PROXY_PAY_FUSE_ERROR = "proxy:pay:fuseerror";

	public static final String REMITTANCERISE_CONTROL_FILTERRESULT = "remittancerisecontrol:filterresult";

	public static final String REMITTANCERISE_CONTROL_AUTH_FREE = "remittancerisecontrol:auth:free";

	public static final String REMITTANCERISE_CONTROL_AUTH_FAIL = "remittancerisecontrol:auth:fail";

	public static final String DRAMAS_EPISODES_JUXING = "dramas:episodes:juxing";

	public static String buildRedisKey(String... keys) {
		StringBuilder keyName = new StringBuilder();
		for (String key : keys) {
			keyName.append(key).append(SEPERATOR);
		}
		return keyName.substring(0, keyName.length() - 1);

	}

	public static String buildUserToken(Long uid){
		return StringUtils.join(USER_TOKEN, SEPERATOR, uid);
	}

	public static String buildLoginTokenKey(String token){
		return StringUtils.join(LOGIN_TOKEN, SEPERATOR, token);
	}

	public static String buildOrderIdKey(Integer storeId, String orderId){
		return StringUtils.join(storeId,"#",orderId);
	}

	public static String buildPayPalOauth2TokenKey() {
		return PAYPAL_OAUTH2_TOKEN;
	}
	public static String buildBakPayPalOauth2TokenKey() {
		return BAK_PAYPAL_OAUTH2_TOKEN;
	}

	public static String buildPayPalOauth2TokenKeyByAccount(String accountName) {
		return PAYPAL_OAUTH2_TOKEN_BY_ACCOUNT + SEPERATOR + accountName;
	}

	public static String buildBakPayPalSwitchKey() {
		return BAK_PAYPAL_SWITCH;
	}
	public static String buildPayPalFuseErrorKey() {
		return PAYPAL_FUSE_ERROR;
	}
	public static String buildCLoseBakPayPalAuth(String auth) {
		return BAK_PAYPAL_CLOSE_AUTH+ SEPERATOR + auth;
	}
	public static String buildBigdataOfferCacheKey() {
		return BIGDATA_OFFER_CACHE;
	}


	public static String buildRemittanceFilterResult(String flag) {
		return REMITTANCERISE_CONTROL_FILTERRESULT+ SEPERATOR + flag;
	}
	public static String buildRemittanceRiseControlFreeAuthKey(String auth) {
		return REMITTANCERISE_CONTROL_AUTH_FREE+ SEPERATOR + auth;
	}
	public static String buildRemittanceRiseControlFailAuthKey(String auth) {
		return REMITTANCERISE_CONTROL_AUTH_FAIL+ SEPERATOR + auth;
	}

	public static final String TASK_ODID = "task:odid";

	/**
	 * 删除僵尸用户数据任务队列
	 * sorted set 结构, 以 用户最后登录时间为分数, value为 userId
	 * */
	public static final String TASK_DEL_ZOMBIES = "task:del:zombies";

	public static String buildBakProxySwitchKey() {
		return BAK_PROXY_SWITCH;
	}

	public static String buildProxyFuseErrorKey() {
		return PROXY_FUSE_ERROR;
	}

	public static String buildBakProxyPaySwitchKey() {
		return BAK_PROXY_PAY_SWITCH;
	}

	public static String buildProxyPayFuseErrorKey() {
		return PROXY_PAY_FUSE_ERROR;
	}

	public static final String PROXY_PAY_CREATE_ORDER_ERROR = "proxy:pay:error:order";

	public static String buildProxyPayFailRecordKey(String proxyPayType, String country) {
		return PROXY_PAY_CREATE_ORDER_ERROR + SEPERATOR + proxyPayType + SEPERATOR + country;
	}

	public static final String PROXY_PAY_CREATE_ORDER_ERROR_RECORD = "proxy:pay:error:order:flag";

	public static String buildProxyPayFailFlagKey(String proxyPayType, String country) {
		return PROXY_PAY_CREATE_ORDER_ERROR_RECORD + SEPERATOR + proxyPayType + SEPERATOR + country;
	}

	public static final String PROXY_PAY_DAILY_QUOTA_RECORD = "proxy:pay:error:daily_quota:flag";

	public static String buildProxyPayDailyQuotaFlagKey(String proxyPayType) {
		return PROXY_PAY_CREATE_ORDER_ERROR_RECORD + SEPERATOR + proxyPayType;
	}

	public static final String PROXY_PAY_IFRAME_DENY = "proxy:pay:platform:iframe:deny";

	public static String buildProxyPayPlatformIframeDenyKey(String proxyPayType) {
		return PROXY_PAY_IFRAME_DENY + SEPERATOR + proxyPayType;
	}

	public static final String PROXY_PAY_CACHE_ORDER = "proxy:pay:cache:order:";

	public static String buildProxyPayOrderCache(String googleId, String sid, String amount) {
		return PROXY_PAY_CACHE_ORDER + googleId + SEPERATOR + sid + SEPERATOR + amount;
	}


	public static final String PROXY_PAY_TYPE_CACHE_ORDER = "proxy:pay:cache:order:type:";

	public static String buildProxyPayOrderTypeCache(String googleId, String sid, String amount) {
		return PROXY_PAY_TYPE_CACHE_ORDER + googleId + SEPERATOR + sid + SEPERATOR + amount;
	}

	public static final String PROXY_PAY_CONFIRM_HASH_CACHE_ORDER = "proxy:pay:cache:order:confirm:hash:";

	public static String buildProxyPayConfirmOrderHashKey(String googleId, String sid) {
		return PROXY_PAY_CONFIRM_HASH_CACHE_ORDER + googleId + SEPERATOR + sid;
	}

}
