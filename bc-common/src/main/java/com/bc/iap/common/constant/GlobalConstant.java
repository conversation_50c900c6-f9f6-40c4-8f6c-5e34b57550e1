package com.bc.iap.common.constant;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * Application config const
 * <p/>
 * Created in 2019.05.24
 * <p/>
 *
 * <AUTHOR>
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class GlobalConstant {

    public final static int VERSION_DOUBLE_BALANCES = 1;

    /**
     * The constant REDIS_COMMAND_STATE_OK.
     */
    public final static String REDIS_COMMAND_STATE_OK = "OK";

    /**
     * The constant UNKNOWN.
     */
    public static final String UNKNOWN = "unknown";
    /**
     * The constant X_FORWARDED_FOR.
     */
    public static final String X_FORWARDED_FOR = "X-Forwarded-For";
    /**
     * The constant X_REAL_IP.
     */
    public static final String X_REAL_IP = "X-Real-IP";
    /**
     * The constant PROXY_CLIENT_IP.
     */
    public static final String PROXY_CLIENT_IP = "Proxy-Client-IP";
    /**
     * The constant WL_PROXY_CLIENT_IP.
     */
    public static final String WL_PROXY_CLIENT_IP = "WL-Proxy-Client-IP";
    /**
     * The constant HTTP_CLIENT_IP.
     */
    public static final String HTTP_CLIENT_IP = "HTTP_CLIENT_IP";
    /**
     * The constant HTTP_X_FORWARDED_FOR.
     */
    public static final String HTTP_X_FORWARDED_FOR = "HTTP_X_FORWARDED_FOR";

    /**
     * The constant LOCALHOST_IP.
     */
    public static final String LOCALHOST_IP = "127.0.0.1";
    /**
     * The constant LOCALHOST_IP_16.
     */
    public static final String LOCALHOST_IP_16 = "0:0:0:0:0:0:0:1";

    /**
     * 常用符号
     *
     * <AUTHOR>
     */
    public static final class Symbol {
        private Symbol() {
        }

        /**
         * The constant COMMA.
         */
        public static final String COMMA = ",";
        /**
         * The constant SPOT.
         */
        public static final String SPOT = ".";
        /**
         * The constant UNDER_LINE.
         */
        public final static String UNDER_LINE = "_";
        /**
         * The constant PER_CENT.
         */
        public final static String PER_CENT = "%";
        /**
         * The constant AT.
         */
        public final static String AT = "@";
        /**
         * The constant PIPE.
         */
        public final static String PIPE = "||";
        /**
         * The constant SHORT_LINE.
         */
        public final static String SHORT_LINE = "-";
        /**
         * The constant SPACE.
         */
        public final static String SPACE = " ";
        /**
         * The constant SLASH.
         */
        public static final String SLASH = "/";
        /**
         * The constant MH.
         */
        public static final String MH = ":";
        /**
         * 取地址符
         */
        public static final String EG = "&";

        /**
         * 问号
         */
        public static final String QM = "?";

    }
}
