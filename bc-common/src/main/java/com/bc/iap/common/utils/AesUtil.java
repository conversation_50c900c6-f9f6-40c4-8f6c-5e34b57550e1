package com.bc.iap.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.DecoderException;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.tuple.Pair;

import javax.crypto.BadPaddingException;
import javax.crypto.IllegalBlockSizeException;
import java.nio.charset.StandardCharsets;

import static java.nio.charset.StandardCharsets.UTF_8;

@Slf4j
public class AesUtil {

    private static final String KEY = "afc1eb23d87b4fb2";
    private static final String IV = "bc0fec701adc8346";

    private static final Pair<String, String> PAIR = Pair.of(KEY, IV);

    /**
     * 使用系统固定密钥/偏移量 加密
     *
     * @return 加密后密文(16进制编码)
     */
    public static String encrypt(String content) throws IllegalBlockSizeException, BadPaddingException {
        byte[] encrypted = AesCipherFactory.getEncryptCipher(PAIR).doFinal(content.getBytes(UTF_8));
        return Hex.encodeHexString(encrypted);
    }

    /**
     * 使用系统固定密钥/偏移量 解密
     *
     * @param hexString 密文(16进制编码)
     */
    public static String decrypt(String hexString) throws IllegalBlockSizeException, BadPaddingException, DecoderException {
        byte[] decrypted = AesCipherFactory.getDecryptCipher(PAIR).doFinal(Hex.decodeHex(hexString));
        return new String(decrypted, UTF_8);
    }

    /**
     * 使用指定密钥/偏移量 加密
     *
     * @return 加密后密文, base64编码
     */
    public static byte[] encryptBase64(Pair<String, String> pair, byte[] content) throws BadPaddingException, IllegalBlockSizeException {
        byte[] encrypted = AesCipherFactory.getEncryptCipher(pair).doFinal(content);
        return Base64.encodeBase64(encrypted);
    }

    public static String encryptBase64String(Pair<String, String> pair, String content) throws BadPaddingException, IllegalBlockSizeException {
        return new String(encryptBase64(pair, content.getBytes(UTF_8)), StandardCharsets.US_ASCII);
    }


    /**
     * 解密
     *
     * @param pair         密钥/偏移量
     * @param base64String 密文(base64编码)
     */
    public static byte[] decryptBase64(Pair<String, String> pair, String base64String) throws BadPaddingException, IllegalBlockSizeException {
        return AesCipherFactory.getDecryptCipher(pair).doFinal(Base64.decodeBase64(base64String));
    }

    public static String decryptBase64ToString(Pair<String, String> pair, String base64String) throws BadPaddingException, IllegalBlockSizeException {
        return new String(decryptBase64(pair, base64String), UTF_8);
    }

    public static String encryptHexString(Pair<String, String> pair, String content) throws BadPaddingException, IllegalBlockSizeException {
        byte[] encrypted = AesCipherFactory.getEncryptCipher(pair).doFinal(content.getBytes(UTF_8));
        return Hex.encodeHexString(encrypted, true);
    }
    /**
     * 由于AES密钥/偏移量长度固定为16,此处将任意secret内容计算 MD5(32) 后, 取前16为密钥, 后16为偏移量
     */
    public static Pair<String, String> getKVPair(String secret) {
        String s = DigestUtils.md5Hex(secret);
        return Pair.of(s.substring(0, 16), s.substring(16));
    }


}
