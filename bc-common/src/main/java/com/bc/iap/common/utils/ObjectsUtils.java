package com.bc.iap.common.utils;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.SimpleTypeConverter;

import java.util.function.Predicate;
import java.util.function.Supplier;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ObjectsUtils {

    public static <T> T defaultIf(final T object, final Predicate<T> predicate, final T defaultValue) {
        return predicate.test(object) ? defaultValue : object;
    }

    public static <T> T defaultIf(final T object, final Predicate<T> predicate, final Supplier<T> defaultValueSupplier) {
        return predicate.test(object) ? defaultValueSupplier.get() : object;
    }

    public static <T> T defaultIfNull(final T object, final T defaultValue) {
        return object == null ? defaultValue : object;
    }

    public static <T> T defaultIfNull(final T object, final Supplier<T> defaultValueSupplier) {
        return object != null ? object : defaultValueSupplier.get();
    }

    public static String defaultIfBlank(final String str, final Supplier<String> defaultStrSupplier) {
        return StringUtils.isBlank(str) ? defaultStrSupplier.get() : str;
    }

    public static <T> T convertIfNotNull(final Object object, T defaultValue, Class<T> clazz) {
        return object == null ? defaultValue : new SimpleTypeConverter().convertIfNecessary(object, clazz);
    }


}
