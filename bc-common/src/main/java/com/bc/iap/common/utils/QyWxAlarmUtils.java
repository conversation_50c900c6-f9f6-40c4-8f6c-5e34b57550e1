package com.bc.iap.common.utils;

import com.alibaba.fastjson.JSON;
import com.bc.iap.common.dto.WechatReq;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class QyWxAlarmUtils {


    public static void sendWarn(String title, String content, String sendUrl) {
        String context = String.format("%s\n%s", title, content);
        WechatReq wechatReq = new WechatReq();
        WechatReq.TextInfo textInfo = new WechatReq.TextInfo();
        textInfo.setContent(context);
        wechatReq.setMsgtype("text");
        textInfo.setContent(textInfo.getContent());
        wechatReq.setText(textInfo);
        String postString = JSON.toJSONString(wechatReq);
        try {
            HttpsUtil.sendPost(sendUrl, postString);
        } catch (Exception e) {
            log.error("代理告警发送错误,请求体{}", postString, e);
        }
    }
}
