package com.bc.iap.common.utils;


import org.apache.commons.collections4.CollectionUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.AbstractMap.SimpleEntry;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.TimeUnit;

public class DateUtils {

    public static final DateTimeFormatter FMT_DAY_SHORT = DateTimeFormatter.ofPattern("MMdd");
    public static final DateTimeFormatter FMT_TIME_SHORT = DateTimeFormatter.ofPattern("HHmmss");


    public static final DateTimeFormatter FMT_DATE_TIME = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    public static final DateTimeFormatter FMT_DATE = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    public static final DateTimeFormatter FMT_RFC_3339 = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");

    public static final DateTimeFormatter FMT_ISO_OFFSET = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssXXX");
    public static final String DATE_PATTERN = "yyyy-MM-dd";
    public static final String DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    public static final int SECONDS_OF_DAY = 3600 * 24;

    /**
     * 获取两个日期相差的天数
     *
     * @param date1
     * @param date2
     * @return
     */
    public static int getBetweenDay(Date date1, Date date2) {
        Calendar d1 = new GregorianCalendar();
        d1.setTime(date1);
        Calendar d2 = new GregorianCalendar();
        d2.setTime(date2);
        int days = d2.get(Calendar.DAY_OF_YEAR) - d1.get(Calendar.DAY_OF_YEAR);
        int y2 = d2.get(Calendar.YEAR);
        if (d1.get(Calendar.YEAR) != y2) {
            do {
                days += d1.getActualMaximum(Calendar.DAY_OF_YEAR);
                d1.add(Calendar.YEAR, 1);
            } while (d1.get(Calendar.YEAR) != y2);
        }
        return days;
    }

    /**
     * 获取指定时区当前时间
     */
    public static final Date getCurrentDate(String timeZone) {
        return getCurrentDate(TimeZone.getTimeZone(timeZone));
    }

    /**
     * 获取特定时区的当前时间
     *
     * @param timeZone
     * @return
     */
    public static final Date getCurrentDate(TimeZone timeZone) {
        TimeZone defaultTimeZone = TimeZone.getDefault();
        long time = Calendar.getInstance(defaultTimeZone).getTimeInMillis();
        return new Date(time + timeZone.getRawOffset() - defaultTimeZone.getRawOffset());
    }

    /**
     * 获取目标时区转换后的时间
     *
     * @param targetDate
     * @param timeZoneStr
     * @return
     */
    public static final Date getTargetDate(Date targetDate, String timeZoneStr) {
        TimeZone targetTimeZone = TimeZone.getTimeZone(timeZoneStr);
        TimeZone defaultTimeZone = TimeZone.getDefault();
        return new Date(targetDate.getTime() + targetTimeZone.getRawOffset() - defaultTimeZone.getRawOffset());
    }

    public static final Date getCurrentDateTimezone0() {
        return getCurrentDate(TimeZone.getTimeZone("GMT+00:00"));
    }


    public static Integer seconds2NextDay(LocalTime time) {
        return SECONDS_OF_DAY - time.toSecondOfDay();
    }
    public static final String getStringDateByTimeZone(String stringDate, String dateFormat, int timeZone) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
        Date date = sdf.parse(stringDate);
        date = DateUtils.addHours(date, timeZone);
        return sdf.format(date);
    }

    /**
     * 获取当前的日期时间戳
     *
     * @return
     */
    public static final Integer getCurrentDayStamp() {
        long now = System.currentTimeMillis() / 1000l;
        long daySecond = 60 * 60 * 24;
        Long dayTime = now - (now + 8 * 3600) % daySecond;
        return dayTime.intValue();
    }

    /**
     * 获取今天凌晨的时间戳(10位)
     *
     * @return
     */
    public static final Long getTodayDayStamp() {
        return getStartOfDay(new Date()).getTime() / 1000;
    }


    public static final Integer getCurrentTimeStamp() {
        return ((Long) (new Date().getTime() / 1000)).intValue();
    }


    public static Date getEndOfDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        return calendar.getTime();
    }

    public static Date getStartOfDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime();
    }

    /**
     * 计算相邻时间戳之间的间隔时间
     */
    public static List<Long> listDurations(List<Long> list) {
        List<Long> durations = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            final Iterator<Long> it = list.iterator();
            Long last = it.next();
            while (it.hasNext()) {
                Long current = it.next();
                durations.add(current - last);
                last = current;
            }
        }
        return durations;
    }

    public static String format(Date date) {
        return format(date, DATE_PATTERN);
    }

    public static String format(Date date, String pattern) {
        if (date != null) {
            SimpleDateFormat df = new SimpleDateFormat(pattern);
            return df.format(date);
        }
        return null;
    }

    public static Date parseDate(String dateTimeString) {
        LocalDateTime localDateTime = LocalDateTime.parse(dateTimeString, FMT_DATE_TIME);
        ZoneId zoneId = ZoneId.systemDefault();
        return Date.from(localDateTime.atZone(zoneId).toInstant());
    }

    public static Date parseDate(String dateTimeString, DateTimeFormatter formatter) {
        LocalDateTime localDateTime = LocalDateTime.parse(dateTimeString, formatter);
        ZoneId zoneId = ZoneId.systemDefault();
        return Date.from(localDateTime.atZone(zoneId).toInstant());
    }

    public static String formatDate(LocalDateTime dateTime, DateTimeFormatter formatter) {
        return dateTime.format(formatter);
    }

    public static String formatDate(ZonedDateTime dateTime, DateTimeFormatter formatter) {
        return dateTime.format(formatter);
    }

    public static Date addDays(Date date, int days) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, days);
        return calendar.getTime();
    }

    public static Date addHours(Date date, int hour) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.HOUR_OF_DAY, hour);
        return calendar.getTime();
    }

    public static Date addMins(Date date, int mins) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MINUTE, mins);
        return calendar.getTime();
    }

    /**
     * 查找指定时间, 所属的时间段
     *
     * @param start           每天开始小时数
     * @param inteval         每个时间段之间间隔(单位小时)
     * @param currentDateTime 当前时间
     * @return 时间段开始时间, 下个时间段开始时间
     */
    public static Entry<LocalDateTime, LocalDateTime> locatePeriod(int start, double inteval, LocalDateTime currentDateTime) {
        LocalTime currentTime = currentDateTime.toLocalTime();
        int second = currentTime.minusHours(start).toSecondOfDay();
        int intevalSecond = (int) (inteval * 3600);
        // 当前处于每天的第几个周期
        int period = second / intevalSecond;

        LocalTime currentPeriod = LocalTime.ofSecondOfDay((period * intevalSecond + start * 3600) % SECONDS_PER_DAY);
        LocalDate currentDate = currentDateTime.toLocalDate();
        LocalDateTime currentPeriodDateTime = LocalDateTime.of(currentPeriod.isBefore(currentTime) ? currentDate : currentDate.minusDays(1), currentPeriod);
        // 下一时间段的开始时间
        int maxPeriod = (int) (SECONDS_PER_DAY / intevalSecond);
        LocalTime nextPeriod = period >= maxPeriod ? LocalTime.of(start, 0) : LocalTime.ofSecondOfDay(((period + 1) * intevalSecond + start * 3600) % SECONDS_PER_DAY);
        LocalDateTime nextPeriodDateTime = LocalDateTime.of(nextPeriod.isBefore(currentTime) ? currentDate.plusDays(1) : currentDate, nextPeriod);

        return new SimpleEntry<>(currentPeriodDateTime, nextPeriodDateTime);
    }

    /**
     * 每天的秒数
     */
    public static final long SECONDS_PER_DAY = TimeUnit.DAYS.toSeconds(1);

    public static final long MILLIS_PER_DAY = TimeUnit.DAYS.toMillis(1);

}
