package com.bc.iap.common.utils;

import com.alibaba.fastjson.JSONObject;
import org.apache.http.Consts;
import org.apache.http.HttpEntity;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpEntityEnclosingRequestBase;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.ssl.TrustStrategy;
import org.apache.http.util.EntityUtils;

import javax.net.ssl.SSLContext;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URI;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.Map;

public class HttpsUtil {

	private static String charset = "utf-8";
	private static String userAgent = "Mozilla/5.0 (Windows NT 6.2; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/54.0.2840.87 Safari/537.36";
	private static int socketTimeout = 300000;
	// 超时时间
	private static int connectTimeout = 300000;

	public static CloseableHttpClient createSSLClientDefault() {
		try {
			SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustStrategy() {
				// 信任所有
				@Override
				public boolean isTrusted(X509Certificate[] chain, String authType) throws CertificateException {
					return true;
				}
			}).build();
			SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext);
			return HttpClients.custom().setSSLSocketFactory(sslsf).build();
		} catch (KeyManagementException e) {
			e.printStackTrace();
		} catch (NoSuchAlgorithmException e) {
			e.printStackTrace();
		} catch (KeyStoreException e) {
			e.printStackTrace();
		}
		return HttpClients.createDefault();
	}

	/**
	 * 参数直接附在url上
	 * 
	 * @param url
	 * @return String
	 */
	public static String sendGet(String url) {
		return excuteHttpGet(url);
	}

	/**
	 * map参数形式
	 * @param url
	 * @param params
	 * @return String
	 */
	public static String sendGet(String url, Map<String, String> params) {
		url += "?";
		for (String key : params.keySet()) {
			url += key + "=" + params.get(key) + "&";
		}
		return excuteHttpGet(url);
	}

	/**
	 * map参数形式(带token的请求)
	 * @param url
	 * @param params
	 * @return String
	 */
	public static String sendGet(String url, String accessToken, Map<String, Object> params) {
		// 构造请求
		CloseableHttpClient httpsclient = createSSLClientDefault();
		HttpEntityEnclosingRequestBase httpEntity = new HttpEntityEnclosingRequestBase() {
			@Override
			public String getMethod() {
				return "GET";
			}
		};
		CloseableHttpResponse response = null;
		try {
			httpEntity.setHeader("Access-Token",accessToken);
			httpEntity.setURI(URI.create(url));
			httpEntity.setEntity(new StringEntity(JSONObject.toJSONString(params), ContentType.APPLICATION_JSON));
			// 设置请求和传输超时时间
			RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(socketTimeout).setConnectTimeout(connectTimeout).build();
			httpEntity.setConfig(requestConfig);
			response = httpsclient.execute(httpEntity);
			if (response != null && response.getStatusLine().getStatusCode() == 200) {
				BufferedReader bufferedReader  = new BufferedReader(new InputStreamReader(response.getEntity().getContent()));
				StringBuffer resultBuffer = new StringBuffer();
				String line = "";
				while((line = bufferedReader.readLine()) != null) {
					resultBuffer.append(line);
				}
				bufferedReader.close();
				String result = resultBuffer.toString();
				return result;
			}

		} catch (ClientProtocolException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			try {
				if (response != null) {
					response.close();
				}
				httpsclient.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return null;
	}

	/**
	 * map参数形式
	 * @param url
	 * @param params
	 * @return String
	 */
	public static String sendPost(String url, Map<String, Object> params) {
		CloseableHttpClient httpsclient = createSSLClientDefault();
		// 构造请求
		HttpPost httpEntity = new HttpPost(url);
		CloseableHttpResponse response = null;
		try {
			httpEntity.setURI(URI.create(url));
			httpEntity.setEntity(new StringEntity(JSONObject.toJSONString(params), ContentType.APPLICATION_JSON));
			// 设置请求和传输超时时间
			RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(socketTimeout).setConnectTimeout(connectTimeout).build();
			httpEntity.setConfig(requestConfig);
			response = httpsclient.execute(httpEntity);
			if (response != null && response.getStatusLine().getStatusCode() == 200) {
				BufferedReader bufferedReader  = new BufferedReader(new InputStreamReader(response.getEntity().getContent()));
				StringBuffer resultBuffer = new StringBuffer();
				String line = "";
				while((line = bufferedReader.readLine()) != null) {
					resultBuffer.append(line);
				}
				bufferedReader.close();
				String result = resultBuffer.toString();
				return result;
			}

		} catch (ClientProtocolException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			try {
				if (response != null) {
					response.close();
				}
				httpsclient.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return null;
	}

	/**
	 * map参数形式(带token)
	 * @param url
	 * @param params
	 * @return String
	 */
	public static String sendPost(String url, String accessToken, Map<String, Object> params) {
		// 构造请求
		CloseableHttpClient httpsclient = createSSLClientDefault();
		HttpPost httpEntity = new HttpPost(url);
		CloseableHttpResponse response = null;
		try {
			httpEntity.setURI(URI.create(url));
			httpEntity.setHeader("Access-Token",accessToken);
			httpEntity.setEntity(new StringEntity(JSONObject.toJSONString(params), ContentType.APPLICATION_JSON));
			// 设置请求和传输超时时间
			RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(socketTimeout).setConnectTimeout(connectTimeout).build();
			httpEntity.setConfig(requestConfig);
			response = httpsclient.execute(httpEntity);
			if (response != null && response.getStatusLine().getStatusCode() == 200) {
				BufferedReader bufferedReader  = new BufferedReader(new InputStreamReader(response.getEntity().getContent()));
				StringBuffer resultBuffer = new StringBuffer();
				String line = "";
				while((line = bufferedReader.readLine()) != null) {
					resultBuffer.append(line);
				}
				bufferedReader.close();
				String result = resultBuffer.toString();
				return result;
			}

		} catch (ClientProtocolException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			try {
				if (response != null) {
					response.close();
				}
				httpsclient.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return null;
	}

	/**
	 * 发送HttpPost请求
	 * 
	 * @param url
	 * @param jsonStr
	 *            参数为json字符串
	 * @return String
	 */
	public static String sendPost(String url, String jsonStr) {
		// 字符串编码
		StringEntity entity = new StringEntity(jsonStr, Consts.UTF_8);
		// 设置content-type
		entity.setContentType("application/json");
		HttpPost httpPost = new HttpPost(url);
		// 防止被当成攻击添加的
		httpPost.setHeader("User-Agent", userAgent);
		// 接收参数设置
		httpPost.setHeader("Accept", "application/json");
		httpPost.setEntity(entity);
		return excuteHttpPost(httpPost);
	}

	/**
	 * 发送不带参数的HttpPost请求
	 * 
	 * @param url
	 * @return String
	 */
	public static String sendPost(String url) {
		// 得到一个HttpPost对象
		HttpPost httpPost = new HttpPost(url);
		// 防止被当成攻击添加的
		httpPost.setHeader("User-Agent", userAgent);
		return excuteHttpPost(httpPost);
	}

	/**
	 * httpPost公用方法
	 * 
	 * @param httpPost
	 * @return
	 */
	private static String excuteHttpPost(HttpPost httpPost) {
		CloseableHttpClient httpsclient = createSSLClientDefault();
		CloseableHttpResponse response = null;
		String result = null;
		RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(socketTimeout).setConnectTimeout(connectTimeout).build();// 设置请求和传输超时时间
		httpPost.setConfig(requestConfig);
		try {
			// 执行post请求
			response = httpsclient.execute(httpPost);
			// 得到entity
			HttpEntity entity = response.getEntity();
			// 得到字符串
			if (entity != null) {
				result = EntityUtils.toString(entity, charset);
			}
		} catch (IOException e) {
			System.out.println("IO异常：" + e.getMessage());
		} finally {
			if (response != null) {
				try {
					response.close();
				} catch (IOException e) {
					System.out.println("关闭Response对象出现异常：" + e.getMessage());
				}
			}
		}
		return result;
	}

	private static String excuteHttpGet(String url) {
		CloseableHttpClient httpsclient = createSSLClientDefault();
		String result = null;
		CloseableHttpResponse response = null;
		try {
			HttpGet httpGet = new HttpGet(url);
			httpGet.setHeader("User-Agent", userAgent);
			RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(socketTimeout).setConnectTimeout(connectTimeout).build();// 设置请求和传输超时时间
			httpGet.setConfig(requestConfig);
			response = httpsclient.execute(httpGet);
			HttpEntity entity = response.getEntity();
			if (entity != null) {
				result = EntityUtils.toString(entity, charset);
			}
		} catch (Exception e) {
			System.out.println(e.getMessage());
		} finally {
			if (response != null) {
				try {
					response.close();
				} catch (IOException e) {
					System.out.println("关闭Response对象出现异常：" + e.getMessage());
				}
			}
		}
		return result;
	}

	/**
	 * 默认utf-8
	 * 
	 * @param charset
	 */
	public static void setCharset(String charset) {
		HttpsUtil.charset = charset;
	}

	public static void setUserAgent(String userAgent) {
		HttpsUtil.userAgent = userAgent;
	}

	/**
	 * 默认2秒
	 * 
	 * @param socketTimeout
	 */
	public static void setSocketTimeout(int socketTimeout) {
		HttpsUtil.socketTimeout = socketTimeout;
	}

	/**
	 * 默认2秒
	 * 
	 * @param connectTimeout
	 */
	public static void setConnectTimeout(int connectTimeout) {
		HttpsUtil.connectTimeout = connectTimeout;
	}
}