package com.bc.iap.common.utils;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.lang3.tuple.Pair;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.util.HashMap;
import java.util.Map;

import static java.nio.charset.StandardCharsets.UTF_8;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class AesCipherFactory {

    private static final String ALGORITHM = "AES/CBC/PKCS5Padding";

    /**
     * 使用线程变量,每个线程单独缓存所有的加(解)密器 , 以 Pair<密钥key,偏移量iv> 作为HashMap的key
     */
    private static final ThreadLocal<Map<Pair<String, String>, Pair<Cipher, Cipher>>> cache = ThreadLocal.withInitial(HashMap::new);


    /**
     * 构建一个加(解)密器
     *
     * @param key  密钥,长度必须为16
     * @param iv   偏移量,长度必须为16
     * @param mode 模式 Cipher.ENCRYPT_MODE, Cipher.DECRYPT_MODE
     */
    @SneakyThrows
    public static Cipher build(String key, String iv, int mode) {
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        cipher.init(mode, new SecretKeySpec(key.getBytes(UTF_8), "AES"), new IvParameterSpec(iv.getBytes(UTF_8)));
        return cipher;
    }

    public static Cipher getCipher(Pair<String, String> pair, int mode) {
        Pair<Cipher, Cipher> cipherPair = cache.get().computeIfAbsent(pair, key ->
                // 同时构建加(解)密器, left:加密,right:解密
                Pair.of(build(pair.getLeft(), pair.getRight(), Cipher.ENCRYPT_MODE), build(pair.getLeft(), pair.getRight(), Cipher.DECRYPT_MODE))
        );
        return mode == Cipher.ENCRYPT_MODE ? cipherPair.getLeft() : cipherPair.getRight();
    }


    public static Cipher getEncryptCipher(Pair<String, String> pair) {
        return getCipher(pair, Cipher.ENCRYPT_MODE);
    }


    public static Cipher getDecryptCipher(Pair<String, String> pair) {
        return getCipher(pair, Cipher.DECRYPT_MODE);
    }

}
