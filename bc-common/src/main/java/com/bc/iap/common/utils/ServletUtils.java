package com.bc.iap.common.utils;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.io.Serializable;
import java.nio.charset.StandardCharsets;
import java.util.Enumeration;
import java.util.Map;


@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ServletUtils {

    public static <T extends Serializable> void returnJson(ServletResponse response, T json) throws IOException {
        returnJson(response, JSON.toJSONString(json));
    }

    public static void returnJson(ServletResponse servletResponse, String json) throws IOException {
        servletResponse.setContentType("application/json; charset=UTF-8");
        servletResponse.setCharacterEncoding("UTF-8");
        try (OutputStreamWriter osw = new OutputStreamWriter(servletResponse.getOutputStream(), StandardCharsets.UTF_8);
             PrintWriter writer = new PrintWriter(osw, true)) {
            // 设置响应内容类型和字符编码
            writer.write(json);
            writer.flush();
        }
    }

    public static Map<String, String[]> getParams(String queryString) {
        if(StringUtils.isBlank(queryString)){
            return Maps.newHashMap();
        }

        String[] params = queryString.split("&");
        Map<String, String[]> resMap = Maps.newTreeMap();
        for (String s : params) {
            String[] param = s.split("=");
            if (param.length >= 2) {
                String key = param[0];
                StringBuilder value = new StringBuilder(param[1]);
                for (int j = 2; j < param.length; j++) {
                    value.append("=").append(param[j]);
                }
                resMap.put(key, new String[]{value.toString()});
            }
        }
        return resMap;
    }

    public static Map<String, String> getHeaders(HttpServletRequest request) {
        Map<String, String> headers = Maps.newHashMap();
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String key = headerNames.nextElement();
            headers.put(key, request.getHeader(key));
        }
        return headers;
    }

    public static String getDomain(String host) {
        return StringUtils.substringAfter(host, ".");
    }

}
