<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.bc.iap</groupId>
        <artifactId>djs-bc-iap</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <name>bc-common</name>
    <artifactId>djs-bc-common</artifactId>
    <description>bc-common</description>
    <dependencies>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>29.0-jre</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zz</groupId>
            <artifactId>http-client</artifactId>
            <version>1.0.0-RELEASE</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.zz</groupId>
            <artifactId>basic-common</artifactId>
            <version>1.4.1-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-reload4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

</project>
