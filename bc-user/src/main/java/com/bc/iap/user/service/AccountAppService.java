package com.bc.iap.user.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bc.iap.user.mapper.TSiteInfoMapper;
import com.bc.iap.user.model.entity.TSiteInfo;
import com.bc.iap.user.model.resp.AppInfoResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class AccountAppService {

    private final TSiteInfoMapper siteInfoMapper;

    @Cacheable(value = "accountAppCache", key = "#appId", unless = "#result == null")
    public AppInfoResp getAppInfo(Long appId) {
        QueryWrapper<TSiteInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("app_id", appId);
        TSiteInfo siteInfo = siteInfoMapper.selectOne(queryWrapper);
        AppInfoResp appInfoResp = new AppInfoResp();
        appInfoResp.setAppId(siteInfo.getId());
        appInfoResp.setSiteName(siteInfo.getLink());
        appInfoResp.setRemark(siteInfo.getRemark());
        return appInfoResp;
    }


    @Cacheable(value = "accountAppCache", key = "#siteName", unless = "#result == null")
    public AppInfoResp getAppInfo(String siteName) {
        QueryWrapper<TSiteInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("link", siteName);
        TSiteInfo siteInfo = siteInfoMapper.selectOne(queryWrapper);
        if (siteInfo == null) {
            return null;
        }
        AppInfoResp appInfoResp = new AppInfoResp();
        appInfoResp.setAppId(siteInfo.getAppId());
        appInfoResp.setSiteName(siteInfo.getLink());
        appInfoResp.setRemark(siteInfo.getRemark());
        return appInfoResp;
    }

}
