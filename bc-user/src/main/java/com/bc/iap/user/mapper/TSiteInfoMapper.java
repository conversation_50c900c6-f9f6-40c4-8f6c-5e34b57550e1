package com.bc.iap.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bc.iap.user.model.entity.TSiteInfo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 * 站点信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-11
 */
public interface TSiteInfoMapper extends BaseMapper<TSiteInfo> {

    @Select("select * from t_site_info where link = #{link} limit 1")
    TSiteInfo selectSiteInfo(@Param("link") String link);
}
