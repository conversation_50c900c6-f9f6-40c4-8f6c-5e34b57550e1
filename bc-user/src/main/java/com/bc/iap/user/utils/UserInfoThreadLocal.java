package com.bc.iap.user.utils;


import com.bc.iap.user.model.dto.UserRespVo;

import java.time.LocalDateTime;
import java.util.TimeZone;

public class UserInfoThreadLocal {

    private static ThreadLocal<UserRespVo> threadLocal = new ThreadLocal<>();

    public static Long getUserId() {
        return threadLocal.get().getUserId();
    }

    public static Long getAppId() {
        return threadLocal.get().getAppId();
    }

    public static UserRespVo getUser() {
        return threadLocal.get();
    }

    public static void setUser(UserRespVo UserRespVo) {
        threadLocal.set(UserRespVo);
    }

    public static void clear() {
        threadLocal.remove();
    }

}
