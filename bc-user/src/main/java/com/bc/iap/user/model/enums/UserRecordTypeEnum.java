package com.bc.iap.user.model.enums;

import lombok.Getter;

@Getter
public enum UserRecordTypeEnum {
    VISIT(0,"浏览"),
    COLLECT(1,"收藏"),
    OTHER(2,"其他"),
    ;
    //    类型, 0=短剧记录，1=收藏记录，2=其他
    private int type;
    private String remark;

    UserRecordTypeEnum(int type, String remark) {
        this.type = type;
        this.remark = remark;
    }

    public static UserRecordTypeEnum getType(int type) {
        for (UserRecordTypeEnum typeEnum : UserRecordTypeEnum.values()) {
            if (typeEnum.getType() == type) {
                return typeEnum;
            }
        }
        return null;
    }
}
