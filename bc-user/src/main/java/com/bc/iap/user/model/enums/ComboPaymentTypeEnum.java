package com.bc.iap.user.model.enums;

import lombok.Getter;

@Getter
public enum ComboPaymentTypeEnum {
    PAYMENT(0,"付款"),
    SUBSCRIBE(1, "订阅"),
    ;
    private int type;
    private String remark;

    ComboPaymentTypeEnum(int type, String remark) {
        this.type = type;
        this.remark = remark;
    }

    public static ComboPaymentTypeEnum getComboType(int comboType) {
        for (ComboPaymentTypeEnum typeEnum : ComboPaymentTypeEnum.values()) {
            if (typeEnum.getType() == comboType) {
                return typeEnum;
            }
        }
        return null;
    }
}
