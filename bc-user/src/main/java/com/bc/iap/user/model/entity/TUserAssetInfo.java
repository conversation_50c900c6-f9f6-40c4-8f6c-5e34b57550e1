package com.bc.iap.user.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 用户资产信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Getter
@Setter
@TableName("t_user_asset_info")
public class TUserAssetInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 产品id
     */
    private Long appId;

    /**
     * 关联用户
     */
    private Long userId;

    /**
     * 订单类型,0剧集,1月卡,2季卡,3年卡
     */
    private Integer orderType;

    /**
     * 关联短剧ID,买的剧集
     */
    private Long dramaId;

    /**
     * 激活时间
     */
    private LocalDateTime activeTime;

    /**
     * 到期时间
     */
    private LocalDateTime expireTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
