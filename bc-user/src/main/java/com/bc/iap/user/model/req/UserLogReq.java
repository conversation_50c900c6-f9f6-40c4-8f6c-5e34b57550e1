package com.bc.iap.user.model.req;

import lombok.Data;

import java.util.List;

/**
 * The type User log req.
 */
@Data
public class UserLogReq {
    private String guestId;
    private List<DramaRecordData> visitData;
    private List<DramaRecordData> collectData;


    @Data
    public static class DramaRecordData {
        //短剧id
        private Long dramaId;
        //进度
        private Integer progress;
        //当前集数
        private Integer currentEpisode;
        //总集数
        private Integer totalEpisode;
        //记录日期
        private String recordTime;
        /**
         * 类型，0短剧记录，1收藏
         */
        private int type;
    }

}
