package com.bc.iap.user.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-10
 */
@Getter
@Setter
@TableName("t_user")
public class TUser implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户名唯一标识
     */
    @TableId(value = "user_id", type = IdType.AUTO)
    private Long userId;

    /**
     * 产品ID
     */
    private Long appId;

    /**
     * 用户名唯一
     */
    private String username;

    /**
     * 头像url
     */
    private String avatar;

    /**
     * 邮箱，唯一
     */
    private String email;

    /**
     * 密码（加密存储）
     */
    private String password;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 状态, 0=禁用，1=未注册, 2=已注册，3=已激活
     */
    private Integer userStatus;

    /**
     * 第三方id
     */
    private String thirdPartId;

    /**
     * 第三方绑定类型（0：未绑定；1：FB ；2：GG；）
     */
    private Integer thirdPartType;

    /**
     * 用户最近登录的时间
     */
    private LocalDateTime loginTime;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
