package com.bc.iap.user.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 站点信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-11
 */
@Getter
@Setter
@TableName("t_site_info")
public class TSiteInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 站点id
     */
    private Long appId;

    /**
     * 站点链接
     */
    private String link;

    /**
     * 链接状态,0启用,1禁用
     */
    private Integer linkStatus;
    /**
     * 站点链接描述
     */
    private String remark;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
