package com.bc.iap.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bc.iap.user.model.entity.TUserAssetInfo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 用户资产信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025 -09-09
 */
public interface TUserAssetInfoMapper extends BaseMapper<TUserAssetInfo> {

    /**
     * Gets user asset list.
     *
     * @param userId the user id
     * @return the user asset list
     */
    @Select("select * from t_user_asset_info where user_id=#{userId}")
    List<TUserAssetInfo> getUserAssetList(@Param("userId") Long userId);
}
