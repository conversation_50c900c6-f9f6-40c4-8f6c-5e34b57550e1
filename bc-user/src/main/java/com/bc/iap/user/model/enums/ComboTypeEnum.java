package com.bc.iap.user.model.enums;

import lombok.Getter;

@Getter
public enum ComboTypeEnum {
    EPISODE(0, 99, "","剧集"),
    WEEK(1, 7,"W", "周卡"),
    MON<PERSON>(2, 31, "M","月卡"),
    SEASON(3, 90,"S", "季卡"),
    YEAR(4, 365, "Y","年卡"),

    ;
    private int type;
    private int day;
    private String unit;
    private String remark;

    ComboTypeEnum(int type, int day, String unit, String remark) {
        this.type = type;
        this.day = day;
        this.unit = unit;
        this.remark = remark;
    }

    public static ComboTypeEnum getComboType(int comboType) {
        for (ComboTypeEnum typeEnum : ComboTypeEnum.values()) {
            if (typeEnum.getType() == comboType) {
                return typeEnum;
            }
        }
        return null;
    }
}
