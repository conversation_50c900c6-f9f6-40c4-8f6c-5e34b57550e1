package com.bc.iap.user.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bc.iap.common.utils.RedisKeyUtil;
import com.bc.iap.common.utils.ServletUtils;
import com.bc.iap.common.utils.SnowFlake;
import com.bc.iap.user.constant.UserConstant;
import com.bc.iap.user.mapper.TUserAssetInfoMapper;
import com.bc.iap.user.mapper.TUserLogMapper;
import com.bc.iap.user.mapper.TUserMapper;
import com.bc.iap.user.model.dto.UserRespVo;
import com.bc.iap.user.model.entity.TUser;
import com.bc.iap.user.model.entity.TUserAssetInfo;
import com.bc.iap.user.model.enums.ComboTypeEnum;
import com.bc.iap.user.model.enums.UserStatusEnum;
import com.bc.iap.user.model.req.UserLoginReq;
import com.bc.iap.user.model.resp.AppInfoResp;
import com.bc.iap.user.model.resp.UserTokenResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Component
@Slf4j
@RequiredArgsConstructor
public class UserService {
    private final TUserAssetInfoMapper tUserAssetInfoMapper;
    private final TUserLogMapper tUserLogMapper;
    private final AccountAppService accountAppService;
    private final TUserMapper tUserMapper;

    private final StringRedisTemplate stringRedisTemplate;

    @Cacheable(value = "userCache", key = "#userId", unless = "#result == null")
    public UserRespVo getUser(Long userId) {
        TUser user = tUserMapper.selectOne(new QueryWrapper<TUser>().eq("user_id", userId));
        return USER_CONVERT.apply(user);
    }

    /**
     * 用户登陆注册接口
     *
     * @param loginReq
     * @return
     */
    public UserRespVo userRegisterOrLogin(HttpServletRequest request, UserLoginReq loginReq) {
        //默认测试站
        String domain = ServletUtils.getDomain(request.getServerName());
        AppInfoResp siteInfo = accountAppService.getAppInfo(domain);
        Long appId = 7373239389677568L;
        if (siteInfo != null) {
            appId = siteInfo.getAppId();
        }
        QueryWrapper<TUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("email", loginReq.getUserEmail());
        queryWrapper.eq("app_id", appId);
        TUser user = tUserMapper.selectOne(queryWrapper);
        boolean isNew = false;
        if (user == null) {
            user = new TUser();
            user.setUserId(SnowFlake.getInstance().nextId());
            //暂时写死一个产品
            user.setAppId(appId);
            user.setEmail(loginReq.getUserEmail());
            user.setChannel(loginReq.getChannel());
            user.setUsername("USER" + RandomStringUtils.randomNumeric(8));
            user.setUserStatus(UserStatusEnum.REGISTER.getStatus());
            user.setLoginTime(LocalDateTime.now());
            tUserMapper.insert(user);
            isNew = true;
        }
        UserRespVo respVo = USER_CONVERT.apply(user);
        if (!isNew) {
            List<TUserAssetInfo> userAssetInfos = tUserAssetInfoMapper.getUserAssetList(user.getUserId());
            if (CollectionUtils.isNotEmpty(userAssetInfos)) {
                Optional<TUserAssetInfo> assetInfoOptional = userAssetInfos.stream().filter(x -> x.getOrderType() != ComboTypeEnum.EPISODE.getType()).findFirst();
                if (assetInfoOptional.isPresent() && assetInfoOptional.get().getExpireTime().isAfter(LocalDateTime.now())) {
                    respVo.setUserStatus(UserStatusEnum.ACTIVE.getStatus());
                    respVo.setActiveTime(assetInfoOptional.get().getActiveTime());
                    respVo.setExpireTime(assetInfoOptional.get().getExpireTime());
                    user.setUserStatus(UserStatusEnum.ACTIVE.getStatus());
                } else {
                    user.setUserStatus(UserStatusEnum.REGISTER.getStatus());
                }
            }
            user.setLoginTime(LocalDateTime.now());
            tUserMapper.updateById(user);
        }
        String token = generateUserToken(user.getUserId(), user.getAppId());
        respVo.setToken(token);
        return respVo;
    }

    public final Function<TUser, UserRespVo> USER_CONVERT = user ->
            new UserRespVo().setUserId(user.getUserId())
                    .setAppId(user.getAppId())
                    .setUserStatus(user.getUserStatus())
                    .setUserName(user.getUsername())
                    .setUserEmail(user.getEmail());

    @Cacheable(value = "userCache", key = "#token", unless = "#result == null")
    public UserTokenResp getUserBaseInfoFromToken(String token) {
        String key = RedisKeyUtil.buildLoginTokenKey(token);
        List<String> values = stringRedisTemplate.<String, String>opsForHash().multiGet(key, Arrays.asList(RedisKeyUtil.LOGIN_TOKEN_UID, RedisKeyUtil.LOGIN_TOKEN_APP_ID));
        if (values.get(0) == null || values.get(1) == null) {
            return null;
        }
        return new UserTokenResp().setUid(Long.parseLong(values.get(0))).setAppId(Long.parseLong(values.get(1)));
    }


    /**
     * 生成用户token，存在事务问题
     * uid-->userTokenKey-->token-->tokenKey-->uid+appid
     * userTokenKey=uid拼接生成
     * tokenKey=token拼接生成
     * token=随机16个字节的字符串
     * redis中：
     * String类型：key=userTokenKey,value=token
     * hash类型：key=tokenKey,value为hash类型的uid+appid
     */
    private String generateUserToken(Long uid, Long appId) {
        String token = RandomStringUtils.random(16, UserConstant.CHARS);
        String tokenKey = RedisKeyUtil.buildLoginTokenKey(token);
        String userTokenKey = RedisKeyUtil.buildUserToken(uid);
        Map<String, Object> map = new HashMap<>();
        map.put(RedisKeyUtil.LOGIN_TOKEN_UID, String.valueOf(uid));
        map.put(RedisKeyUtil.LOGIN_TOKEN_APP_ID, String.valueOf(appId));

        //设置新的token
        stringRedisTemplate.opsForHash().putAll(tokenKey, map);
        stringRedisTemplate.expire(tokenKey, 20, TimeUnit.DAYS);
        //获取旧的tokne
        String oldToken = stringRedisTemplate.opsForValue().get(userTokenKey);
        if (isNotBlank(oldToken)) {
            //删除旧token
            stringRedisTemplate.delete(RedisKeyUtil.buildLoginTokenKey(oldToken));
        }
        //设置用户使用token
        stringRedisTemplate.opsForValue().set(userTokenKey, token, 20, TimeUnit.DAYS);
        return token;
    }

}
