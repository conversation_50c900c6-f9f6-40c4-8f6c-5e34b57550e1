package com.bc.iap.user.model.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
public class UserRespVo {
    private Long appId;
    private Long userId;
    private String userName;
    private String userEmail;
    private String token;
    private Integer userStatus;
    /**
     * 激活时间
     */
    private LocalDateTime activeTime;
    /**
     * 到期时间
     */
    private LocalDateTime expireTime;
}
