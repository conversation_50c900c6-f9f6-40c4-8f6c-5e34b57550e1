package com.bc.iap.user.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bc.iap.common.utils.RequestInfoThreadLocal;
import com.bc.iap.user.mapper.TUserLogMapper;
import com.bc.iap.user.model.dto.UserLogRespVo;
import com.bc.iap.user.model.entity.TUserLog;
import com.bc.iap.user.model.enums.UserRecordTypeEnum;
import com.bc.iap.user.model.req.UserLogGetReq;
import com.bc.iap.user.model.req.UserLogReq;
import com.bc.iap.user.model.resp.UserTokenResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
@Slf4j
public class UserLogService {
    private final TUserLogMapper userLogMapper;
    private final UserService userService;

    public UserLogRespVo getUserLog(HttpServletRequest request, UserLogGetReq logGetReq) {
        String token = request.getHeader(HttpHeaders.AUTHORIZATION);
        Long userId = 0L;
        Long appId = RequestInfoThreadLocal.getAppId();
        if (Strings.isNotBlank(token)) {
            UserTokenResp tokenResp = userService.getUserBaseInfoFromToken(token);
            if (tokenResp != null) {
                userId = tokenResp.getUid();
            }
        }
        QueryWrapper<TUserLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("app_id", appId);
        queryWrapper.eq("guest_id", logGetReq.getGuestId());
        TUserLog userLog = userLogMapper.selectOne(queryWrapper);
        if (userLog == null && userId != 0L) {
            QueryWrapper<TUserLog> queryUserIdWrapper = new QueryWrapper<>();
            queryWrapper.eq("app_id", appId);
            queryWrapper.eq("user_id", userId);
            userLog = userLogMapper.selectOne(queryUserIdWrapper);
        }
        if (userLog != null && Strings.isNotBlank(userLog.getContext())) {
            UserLogRespVo logRespVo = new UserLogRespVo();
            List<UserLogReq.DramaRecordData> userRecordList = JSON.parseArray(userLog.getContext(), UserLogReq.DramaRecordData.class);
            logRespVo.setVisitData(userRecordList.stream().filter(x -> x.getType() == UserRecordTypeEnum.VISIT.getType()).collect(Collectors.toList()));
            logRespVo.setCollectData(userRecordList.stream().filter(x -> x.getType() == UserRecordTypeEnum.COLLECT.getType()).collect(Collectors.toList()));
            return logRespVo;
        }
        return null;
    }

    /**
     * @param request
     * @param userLogReq
     */
    public void recordUserLog(HttpServletRequest request, UserLogReq userLogReq) {
        String token = request.getHeader(HttpHeaders.AUTHORIZATION);
        Long userId = 0L;
        Long appId = RequestInfoThreadLocal.getAppId();
        if (Strings.isNotBlank(token)) {
            UserTokenResp tokenResp = userService.getUserBaseInfoFromToken(token);
            if (tokenResp != null) {
                userId = tokenResp.getUid();
            }
        }
        QueryWrapper<TUserLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("app_id", appId);
        queryWrapper.eq("guest_id", userLogReq.getGuestId());
        TUserLog userLog = userLogMapper.selectOne(queryWrapper);
        if (userLog == null && userId != 0L) {
            QueryWrapper<TUserLog> queryUserIdWrapper = new QueryWrapper<>();
            queryWrapper.eq("app_id", appId);
            queryWrapper.eq("user_id", userId);
            userLog = userLogMapper.selectOne(queryUserIdWrapper);
        }
        boolean isInsert = false;
        List<UserLogReq.DramaRecordData> visitDataList = new ArrayList<>();
        List<UserLogReq.DramaRecordData> collectDataList = new ArrayList<>();
        if (userLog == null) {
            userLog = new TUserLog();
            isInsert = true;
        } else {
            List<UserLogReq.DramaRecordData> userRecordList = JSON.parseArray(userLog.getContext(), UserLogReq.DramaRecordData.class);
            if (CollectionUtils.isNotEmpty(userRecordList)) {
                List<UserLogReq.DramaRecordData> existVisitRecord = userRecordList.stream().filter(x -> x.getType() == 0).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(existVisitRecord)) {
                    visitDataList.addAll(existVisitRecord);
                }
                List<UserLogReq.DramaRecordData> existCollectRecord = userRecordList.stream().filter(x -> x.getType() == 1).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(existCollectRecord)) {
                    collectDataList.addAll(existCollectRecord);
                }
            }
        }
        userLog.setGuestId(userLogReq.getGuestId());
        userLog.setAppId(appId);
        userLog.setUserId(userId);
        setRecordData(isInsert, 0, userLogReq.getVisitData(), visitDataList);
        setRecordData(isInsert, 1, userLogReq.getCollectData(), collectDataList);
        userLog.setContext(JSON.toJSONString(ListUtils.union(visitDataList, collectDataList)));
        if (isInsert) {
            userLogMapper.insert(userLog);
        } else {
            userLogMapper.updateById(userLog);
        }
    }

    private void setRecordData(boolean isInsert, int type,
                               List<UserLogReq.DramaRecordData> userDataList, List<UserLogReq.DramaRecordData> visitDatumList) {
        for (UserLogReq.DramaRecordData visitDatum : userDataList) {
            visitDatum.setType(type);
            if (isInsert) {
                visitDatumList.add(visitDatum);
            } else {
                boolean isMeet = false;
                for (UserLogReq.DramaRecordData recordData : visitDatumList) {
                    if (Objects.equals(recordData.getDramaId(), visitDatum.getDramaId())) {
                        recordData.setProgress(visitDatum.getProgress());
                        recordData.setRecordTime(visitDatum.getRecordTime());
                        recordData.setCurrentEpisode(visitDatum.getCurrentEpisode());
                        isMeet = true;
                        break;
                    }
                }
                //历史没有就插入
                if (!isMeet) {
                    visitDatumList.add(visitDatum);
                }
            }
        }
    }


}
