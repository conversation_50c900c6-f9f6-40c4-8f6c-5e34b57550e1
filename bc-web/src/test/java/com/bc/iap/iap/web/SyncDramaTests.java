package com.bc.iap.iap.web;

import com.bc.iap.web.BcIapWebApplication;
import com.bc.iap.web.scheduler.SyncDramaInfoScheduler;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = BcIapWebApplication.class)
@ActiveProfiles("test")
public class SyncDramaTests {
    @Autowired
    private SyncDramaInfoScheduler syncDramaInfoScheduler;
    @Test
    public void testSync(){
        syncDramaInfoScheduler.syncJuXingDramas();
    }
}
