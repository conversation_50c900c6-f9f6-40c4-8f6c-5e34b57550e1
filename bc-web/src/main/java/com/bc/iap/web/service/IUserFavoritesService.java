package com.bc.iap.web.service;

import com.bc.iap.web.common.dto.AddFavoriteReq;
import com.bc.iap.web.common.dto.GetFavoritesReq;
import com.bc.iap.web.common.dto.RemoveFavoriteReq;
import com.bc.iap.web.common.vo.FavoriteVo;
import com.bc.iap.web.common.vo.PageData;

/**
 * <p>
 * 用户收藏服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-19
 */
public interface IUserFavoritesService {

    /**
     * 添加收藏
     *
     * @param userId 用户ID
     * @param req 添加收藏请求参数
     * @return 是否成功
     */
    boolean addFavorite(Long userId, AddFavoriteReq req);

    /**
     * 删除收藏
     *
     * @param userId 用户ID
     * @param req 删除收藏请求参数
     * @return 是否成功
     */
    boolean removeFavorite(Long userId, RemoveFavoriteReq req);

    /**
     * 分页查询用户收藏列表
     *
     * @param userId 用户ID
     * @param req 查询请求参数
     * @return 分页收藏列表
     */
    PageData<FavoriteVo> getUserFavorites(Long userId, GetFavoritesReq req);

    /**
     * 检查用户是否已收藏某个短剧
     *
     * @param userId 用户ID
     * @param dramaId 短剧ID
     * @return 是否已收藏
     */
    boolean isFavorited(Long userId, Integer dramaId);

    /**
     * 统计用户收藏总数
     *
     * @param userId 用户ID
     * @return 收藏总数
     */
    Long countUserFavorites(Long userId);
}
