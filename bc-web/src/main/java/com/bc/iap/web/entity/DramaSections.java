package com.bc.iap.web.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 短剧模块关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-23
 */
@Getter
@Setter
@TableName("drama_sections")
public class DramaSections implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 短剧ID
     */
    private String dramaId;

    /**
     * 模块ID
     */
    private Integer sectionId;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
