package com.bc.iap.web.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 短剧标签表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-23
 */
@Getter
@Setter
@TableName("tags")
public class Tags implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 标签唯一标识符
     */
    @TableId(value = "tag_id", type = IdType.AUTO)
    private Integer tagId;

    /**
     * 本版本语言代码
     */
    private String language;

    /**
     * 标签名称
     */
    private String name;

    /**
     * 标签描述
     */
    private String description;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 是否发布，默认1，0为未发布
     */
    private Byte status;
}
