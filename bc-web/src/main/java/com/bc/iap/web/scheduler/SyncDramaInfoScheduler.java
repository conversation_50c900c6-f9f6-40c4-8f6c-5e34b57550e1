package com.bc.iap.web.scheduler;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.batmobi.dataxsync.common.model.BaseResponse;
import com.bc.iap.common.utils.MD5Util;
import com.bc.iap.common.utils.RequestUtils;
import com.bc.iap.web.common.dto.resp.JuxingDramaResp;
import com.bc.iap.web.common.enums.DramaSourceEnums;
import com.bc.iap.web.entity.Categories;
import com.bc.iap.web.entity.DramaCategories;
import com.bc.iap.web.entity.ShortDramas;
import com.bc.iap.web.mapper.CategoriesMapper;
import com.bc.iap.web.mapper.DramaCategoriesMapper;
import com.bc.iap.web.mapper.ShortDramasMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.http.HttpStatus;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
@RestController("/back/inter")
@Slf4j
public class SyncDramaInfoScheduler {
    private final DramaCategoriesMapper dramaCategoriesMapper;
    private final ShortDramasMapper shortDramasMapper;
    private final CategoriesMapper categoriesMapper;
    private final RestTemplate restTemplate;

    private final String JUXING_DOMAIN = "https://open-api.zjchjc.cn";
    //剧星剧列表
    private final String JUXING_VIDEO_LIST_PATH = "/api/open/video/list";

    private final String JUXING_EPISODE_LIST_PATH = "/api/open/video/episode/list";
    private final String JUXING_APP_ID = "bc09168862126";
    private final String JUXING_APP_SECRET = "c7a02645275976ecf95213729624f923";

    //获取三方短剧列表
    private final String THIRD_VIDEO_LIST_URL = "http://37.48.118.92:8080/skitsvodposted/sync/api/bydate";


    @RequestMapping("/refresh/juxing/drama")
    public BaseResponse<?> refreshJuxingDrama() {
        syncThirdDramas();
        return BaseResponse.success();
    }

    /**
     * 每小时同步一次任务
     */
    @Scheduled(cron = "0 0 */1 * * ?")
    public void syncThirdDramas() {
        syncJuxingDramas();
        syncOtherDramas();
    }

    /**
     * 同步其他短剧
     */
    public void syncOtherDramas() {

    }

    /**
     * 同步剧星短剧
     */
    public void syncJuxingDramas() {
        Map<String, Object> paramsMap = new HashMap<>();
        long timestamp = System.currentTimeMillis() / 1000;  // 当前秒级时间戳
        paramsMap.put("app_id", JUXING_APP_ID);
        paramsMap.put("timestamp", timestamp);
        paramsMap.put("page", 1);
        paramsMap.put("page_size", 999);
        String paramPath = RequestUtils.createLinkString(paramsMap);
        String sign = MD5Util.MD5(paramPath + "&app_secret=" + JUXING_APP_SECRET);
        String url = JUXING_DOMAIN + JUXING_VIDEO_LIST_PATH + "?" + paramPath + "&sign=" + sign;
        HttpEntity<String> entity = new HttpEntity<>(null, new HttpHeaders());
        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);
        if (response.getStatusCodeValue() != HttpStatus.SC_OK) {
            log.error("同步剧星接口数据异常！body:{}", response.getBody());
        }
        JuxingDramaResp juxingDramaResp = JSON.parseObject(response.getBody(), JuxingDramaResp.class);
        if (juxingDramaResp == null || juxingDramaResp.getData() == null
                || CollectionUtils.isEmpty(juxingDramaResp.getData().getList())) {
            log.error("同步剧星接口解析数据数据异常！body:{}", response.getBody());
            return;
        }
        List<JuxingDramaResp.DramaDTO> dramaEnList = juxingDramaResp.getData().getList()
                .stream().filter(x -> x.getLanguageType() == 2).collect(Collectors.toList());
        int totalCount = 0;
        for (JuxingDramaResp.DramaDTO dramaDto : dramaEnList) {
            QueryWrapper<ShortDramas> shortDramasQueryWrapper = new QueryWrapper<>();
            shortDramasQueryWrapper.eq("third_drama_id", dramaDto.getVideoId());
            shortDramasQueryWrapper.eq("source_id", DramaSourceEnums.juxing.getSource());
            ShortDramas shortDramas = shortDramasMapper.selectOne(shortDramasQueryWrapper);
            if (shortDramas != null) {
                continue;
            }
            shortDramas = new ShortDramas();
            shortDramas.setThirdDramaId(String.valueOf(dramaDto.getVideoId()));
            shortDramas.setTitle(dramaDto.getVideoTitle());
            shortDramas.setDescription(dramaDto.getIntroduce());
            shortDramas.setCountry("en");
            long publishTime = dramaDto.getPublishTime() == 0 ? 1723898056L : dramaDto.getPublishTime();
            shortDramas.setReleaseDate(Instant.ofEpochSecond(publishTime)
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate());
            Random random = new Random();
            double rate = 8.0 + (10.0 - 8.0) * random.nextDouble();
            shortDramas.setRating(new BigDecimal(rate).setScale(1, RoundingMode.HALF_UP));
            shortDramas.setLikesCount(random.nextInt(20000) + 1000);
            shortDramas.setViewsCount(random.nextInt(20000) + 1000);
            shortDramas.setThumbnailUrl(dramaDto.getLogoImg());
            shortDramas.setStatus("released");
            shortDramas.setSourceId(DramaSourceEnums.juxing.getSource());
            shortDramas.setHeat(0);
            shortDramas.setEpisodes(dramaDto.getVideoTotal());
            shortDramas.setAddedDate(LocalDateTime.now());
            shortDramas.setCreatedAt(LocalDateTime.now());
            shortDramas.setUpdatedAt(LocalDateTime.now());
            shortDramas.setMd5Id(UUID.randomUUID().toString());
            List<String> tagList = dramaDto.getTagList();
            tagList = tagList.stream().distinct().collect(Collectors.toList());
            for (String tag : tagList) {
                Categories categories = categoriesMapper.selectOne(new QueryWrapper<Categories>().eq("name", tag));
                if (categories == null) {
                    //插入类目
                    categories = new Categories();
                    categories.setLanguage("en");
                    categories.setName(tag);
                    categories.setDescription(tag);
                    categories.setStatus((byte) 1);
                    categoriesMapper.insert(categories);
                    log.info("插入类目,data:{}", JSON.toJSONString(categories));
                }
                //关联短剧类目
                DramaCategories dramaCategories = new DramaCategories();
                dramaCategories.setDramaId(shortDramas.getMd5Id());
                dramaCategories.setCategoryId(categories.getCategoryId());
                dramaCategories.setCreatedAt(LocalDateTime.now());
                dramaCategories.setUpdatedAt(LocalDateTime.now());
                dramaCategoriesMapper.insert(dramaCategories);
                log.info("插入短剧关联类目,data:{}", JSON.toJSONString(dramaCategories));
            }
            shortDramasMapper.insert(shortDramas);
            totalCount++;
            log.info("插入巨星短剧,data:{}", JSON.toJSONString(shortDramas));
        }
        log.info("总新增剧星短剧,totalCount:{}", totalCount);
    }

}
