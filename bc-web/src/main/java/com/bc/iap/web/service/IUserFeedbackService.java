package com.bc.iap.web.service;

import com.bc.iap.web.common.dto.UserFeedbackReq;
import com.bc.iap.web.entity.TUserFeedback;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 用户反馈服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-19
 */
public interface IUserFeedbackService {

    /**
     * 提交用户反馈
     *
     * @param request 请求对象
     * @param req 反馈请求参数
     * @return 是否成功
     */
    boolean submitFeedback(HttpServletRequest request, UserFeedbackReq req);

    /**
     * 获取反馈统计信息
     *
     * @return 统计信息
     */
    Map<String, Object> getFeedbackStatistics();

    /**
     * 根据支持类型统计反馈数量
     *
     * @return 各类型统计
     */
    List<Map<String, Object>> countBySupportType();

    /**
     * 获取最近的反馈记录
     *
     * @param limit 限制数量
     * @return 反馈记录列表
     */
    List<TUserFeedback> getRecentFeedbacks(Integer limit);
}
