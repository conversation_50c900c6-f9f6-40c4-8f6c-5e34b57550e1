package com.bc.iap.web.service.impl;

import com.bc.iap.user.model.dto.UserRespVo;
import com.bc.iap.user.utils.UserInfoThreadLocal;
import com.bc.iap.web.common.dto.UserFeedbackReq;
import com.bc.iap.web.entity.TUserFeedback;
import com.bc.iap.web.mapper.TUserFeedbackMapper;
import com.bc.iap.web.service.IUserFeedbackService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 用户反馈服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-19
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserFeedbackServiceImpl implements IUserFeedbackService {

    private final TUserFeedbackMapper userFeedbackMapper;

    @Override
    public boolean submitFeedback(HttpServletRequest request, UserFeedbackReq req) {
        log.info("用户提交反馈: name={}, surname={}, email={}, supportType={}, subject={}", 
                req.getName(), req.getSurname(), req.getEmail(), req.getSupportType(), req.getSubject());

        UserRespVo user = UserInfoThreadLocal.getUser();

        if (user == null) {
            throw new RuntimeException("user not login");
        }

        try {
            TUserFeedback feedback = new TUserFeedback();
            feedback.setUserId(user.getUserId());
            feedback.setUsername(user.getUserName());
            feedback.setName(req.getName());
            feedback.setSurname(req.getSurname());
            feedback.setEmail(req.getEmail());
            feedback.setSubject(req.getSubject());
            feedback.setSupportType(req.getSupportType());
            feedback.setMessage(req.getMessage());
            
            // 获取用户IP和User-Agent
            feedback.setIpAddress(getClientIpAddress(request));
            feedback.setUserAgent(request.getHeader("User-Agent"));
            
            feedback.setCreatedAt(LocalDateTime.now());
            feedback.setUpdatedAt(LocalDateTime.now());

            int result = userFeedbackMapper.insert(feedback);
            
            if (result > 0) {
                log.info("用户反馈提交成功: id={}, email={}", feedback.getId(), req.getEmail());
                return true;
            } else {
                log.error("用户反馈提交失败: email={}", req.getEmail());
                return false;
            }
        } catch (Exception e) {
            log.error("用户反馈提交异常: email={}, error={}", req.getEmail(), e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getFeedbackStatistics() {
        log.info("获取反馈统计信息");
        
        Map<String, Object> statistics = new HashMap<>();
        
        // 总反馈数
        Long totalCount = userFeedbackMapper.selectCount(null);
        statistics.put("totalCount", totalCount);
        
        // 今日反馈数
        Long todayCount = userFeedbackMapper.countToday();
        statistics.put("todayCount", todayCount);
        
        // 本月反馈数
        Long thisMonthCount = userFeedbackMapper.countThisMonth();
        statistics.put("thisMonthCount", thisMonthCount);
        
        // 各类型统计
        List<Map<String, Object>> typeStatistics = userFeedbackMapper.countBySupportType();
        statistics.put("supportTypeStatistics", typeStatistics);
        
        log.info("反馈统计信息获取成功: 总数={}, 今日={}, 本月={}", totalCount, todayCount, thisMonthCount);
        return statistics;
    }

    @Override
    public List<Map<String, Object>> countBySupportType() {
        log.info("获取各支持类型反馈统计");
        List<Map<String, Object>> result = userFeedbackMapper.countBySupportType();
        log.info("各支持类型反馈统计获取成功，共{}种类型", result.size());
        return result;
    }

    @Override
    public List<TUserFeedback> getRecentFeedbacks(Integer limit) {
        if (limit == null || limit <= 0) {
            limit = 10; // 默认获取10条
        }
        log.info("获取最近{}条反馈记录", limit);
        Long userId = UserInfoThreadLocal.getUserId();
        if (userId == null) {
            throw new RuntimeException("user not login");
        }
        List<TUserFeedback> feedbacks = userFeedbackMapper.getRecentFeedbacks(userId, limit);
        log.info("获取最近反馈记录成功，共{}条", feedbacks.size());
        return feedbacks;
    }

    /**
     * 获取客户端真实IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0];
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
