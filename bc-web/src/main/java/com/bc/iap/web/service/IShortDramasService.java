package com.bc.iap.web.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bc.iap.common.constant.ErrorCode;
import com.bc.iap.common.dto.exception.BaseException;
import com.bc.iap.common.utils.MD5Util;
import com.bc.iap.common.utils.RedisKeyUtil;
import com.bc.iap.common.utils.RequestUtils;
import com.bc.iap.user.mapper.TUserAssetInfoMapper;
import com.bc.iap.user.model.dto.UserRespVo;
import com.bc.iap.user.model.entity.TUserAssetInfo;
import com.bc.iap.user.model.resp.UserTokenResp;
import com.bc.iap.user.service.UserService;
import com.bc.iap.web.common.dto.CategoryDramasReq;
import com.bc.iap.web.common.dto.EpisodesDetailReq;
import com.bc.iap.web.common.dto.EpisodesReq;
import com.bc.iap.web.common.dto.SearchDramasReq;
import com.bc.iap.web.common.dto.resp.JuxingEpisodeResp;
import com.bc.iap.web.common.enums.DramaSourceEnums;
import com.bc.iap.web.common.vo.*;
import com.bc.iap.web.entity.Categories;
import com.bc.iap.web.entity.ShortDramas;
import com.bc.iap.web.mapper.ShortDramasMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.http.HttpStatus;
import org.apache.logging.log4j.util.Strings;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 短剧主表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-05
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class IShortDramasService {

    private final ShortDramasMapper shortDramasMapper;
    private final UserService userService;
    private final TUserAssetInfoMapper TUserAssetInfoMapper;
    private final RestTemplate restTemplate;
    private final StringRedisTemplate redisTemplate;

//    public HomepageVo homepage(HomepageReq req) {
//        // 定义好需要展示的分类id [] 形式
//        List<Integer> categoryIds = Arrays.asList(1, 2, 3, 4, 5, 6, 7, 8);
//
//        if (req.getSiteDomain() == null) {
//            throw new BaseException(ErrorCode.FAILURE, "siteDomain is null");
//        }
//        HomepageVo resp = new HomepageVo();
//        resp.setSiteDomain(req.getSiteDomain());
//
//        // 查出categoryIds下的所有短剧
//        List<HomepageVoCategoryName> result = shortDramasMapper.homepageByCategoryIds(categoryIds, req.getSourceId());
//        log.info("根据分类ID{}查询到{}条短剧-分类关联记录", categoryIds, result.size());
//
//        // 按分类名称分组，每个分类包含其所有短剧
//        Map<String, List<HomepageVoCategoryName>> groupedByCategory = result.stream()
//                .collect(Collectors.groupingBy(HomepageVoCategoryName::getName));
//
//        log.info("共有{}个分类", groupedByCategory.size());
//
//        // 构建Plate列表
//        List<HomepageVo.Plate> plateList = groupedByCategory.entrySet().stream()
//                .map(entry -> {
//                    HomepageVo.Plate plate = new HomepageVo.Plate();
//                    plate.setPlateName(entry.getKey()); // 分类名称作为plate名称
//
//                    // 构建该分类下的短剧列表，去重相同的短剧
//                    List<HomepageVo.PlateList> dramaList = new ArrayList<>(entry.getValue().stream()
//                            .collect(Collectors.toMap(
//                                    HomepageVoCategoryName::getMd5Id, // 使用md5Id作为key去重
//                                    drama -> new HomepageVo.PlateList(
//                                            drama.getMd5Id(),
//                                            drama.getDramaId(),
//                                            drama.getTitle(),
//                                            drama.getViewsCount(),
//                                            drama.getLikesCount(),
//                                            drama.getThumbnailUrl()
//                                    ),
//                                    (existing, replacement) -> existing // 如果有重复，保留第一个
//                            ))
//                            .values());
//
//
//
//                    plate.setPlateList(dramaList);
//                    log.info("分类 {} 包含 {} 部短剧", entry.getKey(), dramaList.size());
//                    return plate;
//                })
//                .collect(Collectors.toList());
//
//        if (Objects.equals(req.getSiteDomain(), "bingelet.com")) {
//            // 正序
//            plateList.sort(Comparator.comparing(HomepageVo.Plate::getPlateName));
//        }else {
//            // 倒序
//            plateList.sort((o1, o2) -> o2.getPlateName().compareTo(o1.getPlateName()));
//        }
//
//        resp.setPlateList(plateList);
//        log.info("首页数据构建完成，共{}个分类", plateList.size());
//        return resp;
//    }

    public EpisodesVo episodesInfo(EpisodesReq req) {
        if (req.getMd5Id() != null && req.getDramaId() == null) {
            LambdaQueryWrapper<ShortDramas> queryWrapper = new LambdaQueryWrapper<>();
            // 先根据md5Id查询 dramaId
            queryWrapper.eq(ShortDramas::getMd5Id, req.getMd5Id());
            ShortDramas shortDramas = shortDramasMapper.selectOne(queryWrapper);
            if (shortDramas == null) {
                throw new BaseException(ErrorCode.FAILURE, "md5Id or dramaId is null");
            }
            req.setDramaId(String.valueOf(shortDramas.getDramaId()));
        }

        EpisodesVo resp = shortDramasMapper.selectByDramaId(Integer.valueOf(req.getDramaId()));
        if (resp == null) {
            throw new BaseException(ErrorCode.FAILURE, "dramaId is null");
        }
        //剧星兼容
        if (resp.getSourceId() == DramaSourceEnums.juxing.getSource()) {
            resp.setFreeCount(5);
            resp.setTotalCount(resp.getEpisodes());
        }

        // 再查询 6 条除了 req.md5Id 以外的短剧
        LambdaQueryWrapper<ShortDramas> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShortDramas::getSourceId, resp.getSourceId()).ne(ShortDramas::getDramaId, Integer.valueOf(req.getDramaId()));
        queryWrapper.last("limit 6");
        List<ShortDramas> mayLikeList = shortDramasMapper.selectList(queryWrapper);
        resp.setMayLikeList(mayLikeList.stream().map(drama -> new HomepageVo.PlateList(
                drama.getMd5Id(),
                drama.getDramaId(),
                drama.getTitle(),
                drama.getViewsCount(),
                drama.getLikesCount(),
                drama.getThumbnailUrl(),
                drama.getSourceId()
        )).collect(Collectors.toList()));
        return resp;
    }

    public EpisodesDetailVo episodeDetailInfo(EpisodesDetailReq req, String token) {
        log.info("查询剧集详情请求: dramaId={}", req);
        QueryWrapper<ShortDramas> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("drama_id", req.getDramaId());
        ShortDramas shortDramas = shortDramasMapper.selectOne(queryWrapper);
        EpisodesDetailVo resp = new EpisodesDetailVo();
        if (shortDramas.getSourceId() == DramaSourceEnums.juxing.getSource()) {
            resp = getJuxingEpisodesDetail(shortDramas, req.getIndex());
        } else {
            resp = shortDramasMapper.selectDetailByDramaId(req.getDramaId(), req.getIndex());
        }
        if (resp == null) {
            throw new BaseException(ErrorCode.FAILURE, "episode get error");
        }
        resp.setTitle(resp.getDramaTitle());
        log.info("剧集详情{}", resp);
        if (req.getIndex() > resp.getFreeCount() && resp.getIsVip() == 1) {
            // 判断是否为vip用户
            if (token == null) {
                throw new BaseException(ErrorCode.NOT_LOGIN, "not login");
            }
            UserTokenResp userTokenResp = userService.getUserBaseInfoFromToken(token);
            UserRespVo user = userService.getUser(userTokenResp.getUid());
            if (user == null) {
                throw new BaseException(ErrorCode.USER_NOT_EXIST, "user not exist");
            }
            LocalDateTime now = LocalDateTime.now();
            if (user.getUserStatus() == 2) {
                // 判断会员是否过期
                TUserAssetInfo assetInfo = TUserAssetInfoMapper.selectOne(new LambdaQueryWrapper<TUserAssetInfo>()
                        .eq(TUserAssetInfo::getUserId, userTokenResp.getUid())
                );
                if (assetInfo.getExpireTime().isBefore(now)) {
                    throw new BaseException(ErrorCode.USER_NOT_VIP, "vip user expired");
                }
                return resp;
            }
            // 如果不是会员 再判断有没有购买单部的记录
            TUserAssetInfo assetInfo = TUserAssetInfoMapper.selectOne(new LambdaQueryWrapper<TUserAssetInfo>()
                    .eq(TUserAssetInfo::getUserId, userTokenResp.getUid())
                    .eq(TUserAssetInfo::getDramaId, req.getDramaId())
                    .gt(TUserAssetInfo::getExpireTime, now)
                    .eq(TUserAssetInfo::getOrderType, 0)
            );
            if (assetInfo == null) {
                log.info("用户{}不是会员，且没有购买单部剧集{}的记录", userTokenResp.getUid(), req.getDramaId());
                throw new BaseException(ErrorCode.NOT_RECORD, "not buy this drama");
            } else {
                log.info("用户{}不是会员，但是购买了单部剧集{}的记录", userTokenResp.getUid(), req.getDramaId());
                return resp;
            }
        }
        return resp;
    }

    /**
     * 根据指定的分类ID列表获取短剧数据
     *
     * @param categoryIds 分类ID列表
     * @param siteDomain  站点域名
     * @return 按分类分组的短剧数据
     */
    public HomepageVo getShortDramasByCategoryIds(List<Integer> categoryIds, String siteDomain, Integer sourceId) {
        if (categoryIds == null || categoryIds.isEmpty()) {
            throw new BaseException(ErrorCode.FAILURE, "categoryIds cannot be null or empty");
        }

        HomepageVo resp = new HomepageVo();
        resp.setSiteDomain(siteDomain);

        // 根据categoryIds查询drama_categories表，然后关联short_dramas_v1表获取剧的信息
        List<HomepageVoCategoryName> result = shortDramasMapper.homepageByCategoryIds(categoryIds, sourceId);
        log.info("根据分类ID{}查询到{}条短剧-分类关联记录", categoryIds, result.size());

        // 按分类ID分组，每个分类包含其所有短剧
        Map<Integer, List<HomepageVoCategoryName>> groupedByCategoryId = result.stream()
                .collect(Collectors.groupingBy(HomepageVoCategoryName::getCategoryId));

        log.info("共有{}个分类", groupedByCategoryId.size());

        // 按照传入的categoryIds顺序构建Plate列表
        List<HomepageVo.Plate> plateList = new ArrayList<>();
        for (Integer categoryId : categoryIds) {
            List<HomepageVoCategoryName> categoryDramas = groupedByCategoryId.get(categoryId);
            if (categoryDramas != null && !categoryDramas.isEmpty()) {
                HomepageVo.Plate plate = new HomepageVo.Plate();
                // 使用第一个短剧的分类名称作为plate名称（同一分类的名称都相同）
                plate.setPlateName(categoryDramas.get(0).getName());

                // 构建该分类下的短剧列表，去重相同的短剧
                List<HomepageVo.PlateList> dramaList = new ArrayList<>(categoryDramas.stream()
                        .collect(Collectors.toMap(
                                HomepageVoCategoryName::getMd5Id, // 使用md5Id作为key去重
                                drama -> new HomepageVo.PlateList(
                                        drama.getMd5Id(),
                                        drama.getDramaId(),
                                        drama.getTitle(),
                                        drama.getViewsCount(),
                                        drama.getLikesCount(),
                                        drama.getThumbnailUrl(),
                                        drama.getSourceId()
                                ),
                                (existing, replacement) -> existing // 如果有重复，保留第一个
                        ))
                        .values());
                plate.setPlateList(dramaList);
                plateList.add(plate);
                log.info("分类ID {} ({}) 包含 {} 部短剧", categoryId, plate.getPlateName(), dramaList.size());
            } else {
                log.warn("分类ID {} 没有找到对应的短剧数据", categoryId);
            }
        }

        resp.setPlateList(plateList);
        log.info("根据分类ID{}构建完成，共{}个分类，按传入顺序排序", categoryIds, plateList.size());
        return resp;
    }

    /**
     * 根据指定的模块ID列表获取短剧数据
     *
     * @param sectionIds 模块ID列表
     * @param siteDomain  站点域名
     * @param sourceId   来源ID
     * @return 按分类分组的短剧数据
     */
    public HomepageVo homepage(List<Integer> sectionIds, String siteDomain, Integer sourceId) {
        if (sectionIds == null || sectionIds.isEmpty()) {
            throw new BaseException(ErrorCode.FAILURE, "sectionIds cannot be null or empty");
        }

        HomepageVo resp = new HomepageVo();
        resp.setSiteDomain(siteDomain);

        // 根据sectionIds查询drama_sections表，然后关联short_dramas_v1表获取剧的信息
        List<HomepageVoSectionName> result = shortDramasMapper.homepageBySectionIds(sectionIds, sourceId);
        log.info("根据分类ID{}查询到{}条短剧-分类关联记录", sectionIds, result.size());

        // 按分类ID分组，每个分类包含其所有短剧
        Map<Integer, List<HomepageVoSectionName>> groupedBySectionId = result.stream()
                .collect(Collectors.groupingBy(HomepageVoSectionName::getSectionId));

        log.info("共有{}个分类", groupedBySectionId.size());

        // 按照传入的sectionIds顺序构建Plate列表
        List<HomepageVo.Plate> plateList = new ArrayList<>();
        for (Integer sectionId : sectionIds) {
            List<HomepageVoSectionName> sectionDramas = groupedBySectionId.get(sectionId);
            if (sectionDramas != null && !sectionDramas.isEmpty()) {
                HomepageVo.Plate plate = new HomepageVo.Plate();
                // 使用第一个短剧的分类名称作为plate名称（同一分类的名称都相同）
                plate.setPlateName(sectionDramas.get(0).getName());

                // 构建该分类下的短剧列表，去重相同的短剧
                List<HomepageVo.PlateList> dramaList = new ArrayList<>(sectionDramas.stream()
                        .collect(Collectors.toMap(
                                HomepageVoSectionName::getMd5Id, // 使用md5Id作为key去重
                                drama -> new HomepageVo.PlateList(
                                        drama.getMd5Id(),
                                        drama.getDramaId(),
                                        drama.getTitle(),
                                        drama.getViewsCount(),
                                        drama.getLikesCount(),
                                        drama.getThumbnailUrl(),
                                        drama.getSourceId()
                                ),
                                (existing, replacement) -> existing // 如果有重复，保留第一个
                        ))
                        .values());
                plate.setPlateList(dramaList);
                plateList.add(plate);
                log.info("模块ID {} ({}) 包含 {} 部短剧", sectionId, plate.getPlateName(), dramaList.size());
            } else {
                log.warn("模块ID {} 没有找到对应的短剧数据", sectionId);
            }
        }

        resp.setPlateList(plateList);
        log.info("根据模块ID{}构建完成，共{}个模块，按传入顺序排序", sectionIds, plateList.size());
        return resp;
    }

    private final String JUXING_DOMAIN = "https://open-api.zjchjc.cn";
    private final String JUXING_EPISODE_LIST_PATH = "/api/open/video/episode/list";
    private final String JUXING_APP_ID = "bc09168862126";
    private final String JUXING_APP_SECRET = "c7a02645275976ecf95213729624f923";


    public EpisodesDetailVo getJuxingEpisodesDetail(ShortDramas shortDramas, Integer episodeId) {
        String episodesCacheKey = RedisKeyUtil.buildRedisKey(RedisKeyUtil.DRAMAS_EPISODES_JUXING, String.valueOf(shortDramas.getSourceId()), shortDramas.getThirdDramaId());
        String cacheResult = redisTemplate.opsForValue().get(episodesCacheKey);
        if (Strings.isNotBlank(cacheResult)) {
            List<JuxingEpisodeResp.DataDTO> dataDTOS = JSON.parseArray(cacheResult, JuxingEpisodeResp.DataDTO.class);
            if (CollectionUtils.isNotEmpty(dataDTOS)) {
                JuxingEpisodeResp.DataDTO dataDTO = dataDTOS.stream().filter(x -> Objects.equals(x.getEpisodeNo(), episodeId)).findFirst().get();
                LocalDateTime tomorrow = LocalDateTime.now().plusHours(1);
                long timestampSeconds = tomorrow.atZone(ZoneId.systemDefault())
                        .toEpochSecond();
                //距离时间大于1小时，需要更新
                if (timestampSeconds < dataDTO.getExpireTime()) {
                    return buildJuxingEpisodeDetail(shortDramas, dataDTO, episodeId);
                }
            }
        }
        Map<String, Object> paramsMap = new HashMap<>();
        long timestamp = System.currentTimeMillis() / 1000;  // 当前秒级时间戳
        paramsMap.put("app_id", JUXING_APP_ID);
        paramsMap.put("timestamp", timestamp);
        paramsMap.put("video_id", shortDramas.getThirdDramaId());
        // 获取当前时间加1天
        LocalDateTime tomorrow = LocalDateTime.now().plusDays(1);
        // 转换为时间戳（秒）
        long timestampSeconds = tomorrow.atZone(ZoneId.systemDefault())
                .toEpochSecond();
        paramsMap.put("expire_time", timestampSeconds);
        String paramPath = RequestUtils.createLinkString(paramsMap);
        String sign = MD5Util.MD5(paramPath + "&app_secret=" + JUXING_APP_SECRET);
        String url = JUXING_DOMAIN + JUXING_EPISODE_LIST_PATH + "?" + paramPath + "&sign=" + sign;
        HttpEntity<String> entity = new HttpEntity<>(null, new HttpHeaders());
        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);
        if (response.getStatusCodeValue() != HttpStatus.SC_OK) {
            log.error("获取剧星剧集接口数据异常！body:{}", response.getBody());
            return null;
        }
        JuxingEpisodeResp episodeResp = JSON.parseObject(response.getBody(), JuxingEpisodeResp.class);
        List<JuxingEpisodeResp.DataDTO> episodeList = episodeResp.getData();
        //插入缓存
        redisTemplate.opsForValue().set(episodesCacheKey, JSON.toJSONString(episodeList), 23, TimeUnit.HOURS);
        JuxingEpisodeResp.DataDTO dataDTO = episodeList.stream().filter(x -> Objects.equals(x.getEpisodeNo(), episodeId)).findFirst().get();
        return buildJuxingEpisodeDetail(shortDramas, dataDTO, episodeId);
    }

    public EpisodesDetailVo buildJuxingEpisodeDetail(ShortDramas shortDramas,JuxingEpisodeResp.DataDTO dataDTO, Integer episodeId){
        EpisodesDetailVo episodesDetailVo = new EpisodesDetailVo();
        episodesDetailVo.setMd5Id(shortDramas.getMd5Id());
        episodesDetailVo.setEpisodeId(episodeId);
        episodesDetailVo.setDramaId(shortDramas.getDramaId());
        episodesDetailVo.setEpisodeNumber(dataDTO.getEpisodeNo());
        episodesDetailVo.setEpisodeTitle(dataDTO.getEpisodeTitle());
        episodesDetailVo.setDramaTitle(shortDramas.getTitle());
        episodesDetailVo.setTitle(shortDramas.getTitle());
        episodesDetailVo.setDescription(shortDramas.getDescription());
        episodesDetailVo.setViewsCount(shortDramas.getViewsCount());
        episodesDetailVo.setLikesCount(shortDramas.getLikesCount());
        episodesDetailVo.setReleaseDate(shortDramas.getReleaseDate().format(DateTimeFormatter.ISO_DATE));
        episodesDetailVo.setDuration(dataDTO.getDuration());
        episodesDetailVo.setTotalCount(shortDramas.getEpisodes());
        episodesDetailVo.setFreeCount(5);
        episodesDetailVo.setIsVip(episodeId > 5 ? 1 : 0);
        episodesDetailVo.setSourceId(shortDramas.getSourceId());
        episodesDetailVo.setVideoFileId(dataDTO.getTcplayerFileId());
        episodesDetailVo.setVideoAppId(dataDTO.getTcplayerAppId());
        episodesDetailVo.setVideoSign(dataDTO.getTcplayerSign());
        episodesDetailVo.setThumbnailUrl(shortDramas.getThumbnailUrl());
        return episodesDetailVo;
    }

    /**
     * 根据标题搜索短剧
     *
     * @param req 搜索请求参数
     * @return 分页搜索结果
     */
    public PageData<SearchDramasVo> searchDramasByTitle(SearchDramasReq req) {
        log.info("搜索短剧请求: keyword={}, sourceId={}, pageNum={}, pageSize={}",
                req.getKeyword(), req.getSourceId(), req.getPageNum(), req.getPageSize());

        // 创建分页对象
        Page<SearchDramasVo> page = new Page<>(req.getPageNum(), req.getPageSize());

        // 执行分页查询
        Page<SearchDramasVo> result = shortDramasMapper.searchDramasByTitle(page, req.getKeyword(), req.getSourceId());

        log.info("搜索短剧完成: 关键词={}, 总记录数={}, 当前页={}, 每页大小={}",
                req.getKeyword(), result.getTotal(), result.getCurrent(), result.getSize());

        // 构建返回结果
        return new PageData<>(
                (int) result.getCurrent(),
                (int) result.getSize(),
                result.getTotal(),
                (int) result.getPages(),
                result.getRecords().size(),
                result.getRecords()
        );
    }

    /**
     * 根据分类ID列表查询剧列表
     *
     * @param categoryIds 分类ID列表
     * @param sourceId 来源ID（可选）
     * @return 剧列表
     */
    public List<ShortDramas> getDramasByCategoryIds(List<Integer> categoryIds, Integer sourceId) {
        log.info("根据分类ID列表查询剧列表: categoryIds={}, sourceId={}", categoryIds, sourceId);

        List<HomepageVoCategoryName> result = shortDramasMapper.getDramasByCategoryIds(categoryIds, sourceId);
        log.info("根据分类ID列表查询到{}条剧记录", result.size());

        // 转换为ShortDramas列表
        List<ShortDramas> dramas = result.stream()
                .map(item -> (ShortDramas) item)
                .collect(Collectors.toList());

        return dramas;
    }

    /**
     * 根据模块ID列表查询剧列表
     *
     * @param sectionIds 模块ID列表
     * @param sourceId 来源ID（可选）
     * @return 剧列表
     */
    public List<ShortDramas> getDramasBySectionIds(List<Integer> sectionIds, Integer sourceId) {
        log.info("根据模块ID列表查询剧列表: sectionIds={}, sourceId={}", sectionIds, sourceId);

        List<HomepageVoSectionName> result = shortDramasMapper.getDramasBySectionIds(sectionIds, sourceId);
        log.info("根据模块ID列表查询到{}条剧记录", result.size());

        // 转换为ShortDramas列表
        List<ShortDramas> dramas = result.stream()
                .map(item -> (ShortDramas) item)
                .collect(Collectors.toList());

        return dramas;
    }

    /**
     * 根据标签ID列表查询剧列表
     *
     * @param tagIds 标签ID列表
     * @param sourceId 来源ID（可选）
     * @return 剧列表
     */
    public List<ShortDramas> getDramasByTagIds(List<Integer> tagIds, Integer sourceId) {
        log.info("根据标签ID列表查询剧列表: tagIds={}, sourceId={}", tagIds, sourceId);

        List<HomepageVoTagName> result = shortDramasMapper.getDramasByTagIds(tagIds, sourceId);
        log.info("根据标签ID列表查询到{}条剧记录", result.size());

        // 转换为ShortDramas列表
        List<ShortDramas> dramas = result.stream()
                .map(item -> (ShortDramas) item)
                .collect(Collectors.toList());

        return dramas;
    }

}
