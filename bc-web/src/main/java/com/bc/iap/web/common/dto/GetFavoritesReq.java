package com.bc.iap.web.common.dto;

import lombok.Data;

import javax.validation.constraints.Min;

/**
 * 查询收藏列表请求类
 */
@Data
public class GetFavoritesReq {

    /**
     * 当前页码，默认为1
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;

    /**
     * 每页大小，默认为10
     */
    @Min(value = 1, message = "每页大小必须大于0")
    private Integer pageSize = 10;

    /**
     * 来源ID（可选筛选条件）
     */
    private Integer sourceId;
}
