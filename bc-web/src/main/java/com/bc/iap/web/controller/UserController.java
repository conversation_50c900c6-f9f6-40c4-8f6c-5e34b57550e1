package com.bc.iap.web.controller;

import com.batmobi.dataxsync.common.model.BaseResponse;
import com.bc.iap.user.model.req.UserLogGetReq;
import com.bc.iap.user.model.req.UserLogReq;
import com.bc.iap.user.model.req.UserLoginReq;
import com.bc.iap.user.service.UserLogService;
import com.bc.iap.user.service.UserService;
import com.bc.iap.user.utils.UserInfoThreadLocal;
import com.bc.iap.web.common.dto.AddFavoriteReq;
import com.bc.iap.web.common.dto.GetFavoritesReq;
import com.bc.iap.web.common.dto.RemoveFavoriteReq;
import com.bc.iap.web.common.dto.UserFeedbackReq;
import com.bc.iap.web.common.vo.FavoriteVo;
import com.bc.iap.web.common.vo.PageData;
import com.bc.iap.web.entity.TUserFeedback;
import com.bc.iap.web.service.IUserFavoritesService;
import com.bc.iap.web.service.IUserFeedbackService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/user")
@Slf4j
@Validated
public class UserController {

    private final UserService userService;

    private final UserLogService userLogService;

    private final IUserFeedbackService userFeedbackService;

    private final IUserFavoritesService userFavoritesService;

    @RequestMapping("/login")
    public BaseResponse<?> login(HttpServletRequest request, @RequestBody UserLoginReq userLoginReq) {
        return BaseResponse.success(userService.userRegisterOrLogin(request, userLoginReq));
    }

    @RequestMapping("/get/record")
    public BaseResponse<?> record(HttpServletRequest request, @RequestBody UserLogGetReq userLogGetReq) {
        return BaseResponse.success( userLogService.getUserLog(request, userLogGetReq));
    }

    @RequestMapping("/record")
    public BaseResponse<?> record(HttpServletRequest request, @RequestBody UserLogReq userLogReq) {
        userLogService.recordUserLog(request, userLogReq);
        return BaseResponse.success();
    }

    /**
     * 用户反馈提交接口
     * 用户提交反馈信息，包括姓名、邮箱、主题、支持类型和消息内容
     *
     * @param request HTTP请求对象
     * @param req 用户反馈请求参数
     * @return 提交结果
     */
    @PostMapping("/feedback")
    public BaseResponse<?> submitFeedback(HttpServletRequest request, @Valid @RequestBody UserFeedbackReq req) {
        log.info("用户提交反馈: name={} {}, email={}, supportType={}",
                req.getName(), req.getSurname(), req.getEmail(), req.getSupportType());

        boolean success = userFeedbackService.submitFeedback(request, req);

        if (success) {
            log.info("用户反馈提交成功: email={}", req.getEmail());
            return BaseResponse.success("反馈提交成功，我们会尽快处理您的反馈");
        } else {
            log.error("用户反馈提交失败: email={}", req.getEmail());
            return BaseResponse.fail(400,"反馈提交失败，请稍后重试");
        }
    }

    /**
     * 获取反馈统计信息接口
     * 返回反馈的统计数据，包括总数、今日数量、本月数量和各类型统计
     *
     * @return 统计信息
     */
    @GetMapping("/feedback/statistics")
    public BaseResponse<?> getFeedbackStatistics() {
        log.info("获取反馈统计信息");
        return BaseResponse.success(userFeedbackService.getFeedbackStatistics());
    }

    /**
     * 获取各支持类型反馈统计接口
     * 返回各个支持类型的反馈数量统计
     *
     * @return 各类型统计
     */
    @GetMapping("/feedback/support-type-statistics")
    public BaseResponse<?> getSupportTypeStatistics() {
        log.info("获取各支持类型反馈统计");
        return BaseResponse.success(userFeedbackService.countBySupportType());
    }

    /**
     * 获取最近反馈记录接口
     * 返回最近的反馈记录列表
     *
     * @param limit 限制数量，默认10条
     * @return 最近反馈记录
     */
    @GetMapping("/feedback/recent")
    public BaseResponse<List<TUserFeedback>> getRecentFeedbacks(@RequestParam(defaultValue = "10") Integer limit) {
        log.info("获取最近{}条反馈记录", limit);
        return BaseResponse.success(userFeedbackService.getRecentFeedbacks(limit));
    }

    /**
     * 添加收藏接口
     * 用户收藏指定的短剧
     *
     * @param request HTTP请求对象
     * @param req 添加收藏请求参数
     * @return 添加结果
     */
    @PostMapping("/favorites/add")
    public BaseResponse<?> addFavorite(HttpServletRequest request, @Valid @RequestBody AddFavoriteReq req) {
        Long userId = UserInfoThreadLocal.getUserId();

        log.info("用户添加收藏: userId={}, dramaId={}", userId, req.getDramaId());

        boolean success = userFavoritesService.addFavorite(userId, req);

        if (success) {
            log.info("用户收藏添加成功: userId={}, dramaId={}", userId, req.getDramaId());
            return BaseResponse.success("收藏成功");
        } else {
            log.error("用户收藏添加失败: userId={}, dramaId={}", userId, req.getDramaId());
            return BaseResponse.fail(400,"收藏失败，请稍后重试");
        }
    }

    /**
     * 删除收藏接口
     * 用户取消收藏指定的短剧
     *
     * @param request HTTP请求对象
     * @param req 删除收藏请求参数
     * @return 删除结果
     */
    @PostMapping("/favorites/remove")
    public BaseResponse<String> removeFavorite(HttpServletRequest request, @Valid @RequestBody RemoveFavoriteReq req) {

        Long userId = UserInfoThreadLocal.getUserId();

        log.info("用户删除收藏: userId={}, dramaId={}", userId, req.getDramaId());

        boolean success = userFavoritesService.removeFavorite(userId, req);

        if (success) {
            log.info("用户收藏删除成功: userId={}, dramaId={}", userId, req.getDramaId());
            return BaseResponse.success("取消收藏成功");
        } else {
            log.warn("用户收藏删除失败: userId={}, dramaId={}", userId, req.getDramaId());
            return BaseResponse.success("取消收藏成功"); // 即使没有收藏记录也返回成功
        }
    }

    /**
     * 查询收藏列表接口
     * 分页查询用户的收藏列表
     *
     * @param request HTTP请求对象
     * @param req 查询收藏列表请求参数
     * @return 分页收藏列表
     */
    @PostMapping("/favorites/list")
    public BaseResponse<PageData<FavoriteVo>> getFavorites(HttpServletRequest request, @Valid @RequestBody GetFavoritesReq req) {
        Long userId = UserInfoThreadLocal.getUserId();

        log.info("查询用户收藏列表: userId={}, pageNum={}, pageSize={}", userId, req.getPageNum(), req.getPageSize());

        PageData<FavoriteVo> result = userFavoritesService.getUserFavorites(userId, req);

        log.info("查询用户收藏列表成功: userId={}, 总记录数={}, 返回{}条记录",
                userId, result.getTotal(), result.getList().size());

        return BaseResponse.success(result);
    }

    /**
     * 检查收藏状态接口
     * 检查用户是否已收藏指定短剧
     *
     * @param request HTTP请求对象
     * @param dramaId 短剧ID
     * @return 是否已收藏
     */
    @GetMapping("/favorites/check")
    public BaseResponse<Boolean> checkFavorite(HttpServletRequest request, @RequestParam Integer dramaId) {
        Long userId = UserInfoThreadLocal.getUserId();

        log.info("检查用户收藏状态: userId={}, dramaId={}", userId, dramaId);

        boolean isFavorited = userFavoritesService.isFavorited(userId, dramaId);

        log.info("用户收藏状态检查完成: userId={}, dramaId={}, isFavorited={}", userId, dramaId, isFavorited);

        return BaseResponse.success(isFavorited);
    }

    /**
     * 统计收藏总数接口
     * 获取用户的收藏总数
     *
     * @param request HTTP请求对象
     * @return 收藏总数
     */
    @GetMapping("/favorites/count")
    public BaseResponse<Long> getFavoritesCount(HttpServletRequest request) {
        Long userId = UserInfoThreadLocal.getUserId();

        log.info("统计用户收藏总数: userId={}", userId);

        Long count = userFavoritesService.countUserFavorites(userId);

        log.info("用户收藏总数统计完成: userId={}, count={}", userId, count);

        return BaseResponse.success(count);
    }


}
