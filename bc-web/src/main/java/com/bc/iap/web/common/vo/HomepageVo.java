package com.bc.iap.web.common.vo;

import lombok.Data;

import java.util.List;

@Data
public class HomepageVo {
    private String siteDomain;
    private List<Plate> plateList;

    @Data
    public static class Plate {
        private String plateName;
        private List<PlateList> plateList;
    }

    @Data
    public static class PlateList {
        private String md5Id;
        private Integer dramaId;
        private String title;
        private Integer viewsCount;
        private Integer likesCount;
        private String thumbnailUrl;
        private Integer sourceId;

        public PlateList(String md5Id, Integer dramaId,  String title, Integer viewsCount, Integer likesCount, String thumbnailUrl, Integer sourceId) {
            this.md5Id = md5Id;
            this.dramaId = dramaId;
            this.title = title;
            this.viewsCount = viewsCount;
            this.likesCount = likesCount;
            this.thumbnailUrl = thumbnailUrl;
            this.sourceId = sourceId;
        }
    }



}

