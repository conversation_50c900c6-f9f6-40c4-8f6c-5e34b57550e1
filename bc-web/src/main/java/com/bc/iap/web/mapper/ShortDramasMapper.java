package com.bc.iap.web.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bc.iap.web.common.vo.*;
import com.bc.iap.web.entity.Categories;
import com.bc.iap.web.entity.ShortDramas;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 短剧主表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-05
 */

public interface ShortDramasMapper extends BaseMapper<ShortDramas> {

    // 根据 dramaId 查询 剧集信息
    EpisodesVo selectByDramaId(@Param("dramaId") Integer dramaId);


    // 根据 dramaId 和 index 查询剧集详情
    EpisodesDetailVo selectDetailByDramaId(@Param("dramaId") String dramaId, @Param("index") Integer index);

//    @Select("SELECT " +
//            "short_dramas_v1.*, " +
//            "drama_categories.category_id, " +
//            "categories.name " +
//            "FROM short_dramas_v1 " +
//            "INNER JOIN drama_categories " +
//            "ON short_dramas_v1.md5_id = drama_categories.drama_id " +
//            "INNER JOIN categories " +
//            "ON drama_categories.category_id = categories.category_id " +
//            "WHERE categories.status = 1 " +
//            "ORDER BY short_dramas_v1.drama_id")
//    List<HomepageVoCategoryName> homepage();

    // 根据分类ID列表查询首页
    List<HomepageVoCategoryName> homepageByCategoryIds(@Param("categoryIds") List<Integer> categoryIds, @Param("sourceId") Integer sourceId);

    // 根据模块ID列表查询首页
    List<HomepageVoSectionName> homepageBySectionIds(@Param("sectionIds") List<Integer> sectionIds, @Param("sourceId") Integer sourceId);

    // 根据标题搜索短剧（支持分页）
    Page<SearchDramasVo> searchDramasByTitle(Page<SearchDramasVo> page, @Param("keyword") String keyword, @Param("sourceId") Integer sourceId);

    // 根据来源ID和分类ID查询短剧（支持分页）
    Page<CategoryDramasVo> getDramasByCategory(Page<CategoryDramasVo> page, @Param("sourceId") Integer sourceId, @Param("categoryId") Integer categoryId);

    // 根据来源ID查询该来源下的所有分类ID
    List<Integer> getCategoryIdsBySourceId(@Param("sourceId") Integer sourceId);

    // 获取所有分类
    List<Categories> getAllCategories();

    // 根据来源ID获取分类
    List<Categories> getCategoriesBySourceId(@Param("sourceId") Integer sourceId);
}
