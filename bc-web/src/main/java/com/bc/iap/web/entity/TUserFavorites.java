package com.bc.iap.web.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 用户收藏表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-19
 */
@Getter
@Setter
@TableName("t_user_favorites")
public class TUserFavorites implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 短剧ID
     */
    private Integer dramaId;

    /**
     * 短剧MD5ID
     */
    private String dramaMd5Id;

    /**
     * 短剧标题（冗余字段，便于查询）
     */
    private String dramaTitle;

    /**
     * 短剧封面图（冗余字段）
     */
    private String dramaThumbnailUrl;

    /**
     * 来源ID
     */
    private Integer sourceId;

    /**
     * 收藏时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
