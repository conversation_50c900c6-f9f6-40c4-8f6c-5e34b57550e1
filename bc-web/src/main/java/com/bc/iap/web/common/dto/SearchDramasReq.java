package com.bc.iap.web.common.dto;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

/**
 * 搜索短剧请求类
 */
@Data
public class SearchDramasReq {
    
    /**
     * 搜索关键词（标题）
     */
    @NotBlank(message = "搜索关键词不能为空")
    private String keyword;
    
    /**
     * 来源ID（可选）
     */
    private Integer sourceId;
    
    /**
     * 当前页码，默认为1
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;
    
    /**
     * 每页大小，默认为10
     */
    @Min(value = 1, message = "每页大小必须大于0")
    private Integer pageSize = 10;
}
