package com.bc.iap.web.common.vo;

import com.bc.iap.web.entity.ShortDramas;
import lombok.Data;

import java.util.List;

/**
 * 分组剧列表返回VO
 */
@Data
public class GroupedDramasVo {
    
    /**
     * 分组ID（分类ID/模块ID/标签ID）
     */
    private Integer groupId;
    
    /**
     * 分组名称（分类名称/模块名称/标签名称）
     */
    private String groupName;
    
    /**
     * 该分组下的剧列表
     */
    private List<ShortDramas> dramas;
    
    public GroupedDramasVo(Integer groupId, String groupName, List<ShortDramas> dramas) {
        this.groupId = groupId;
        this.groupName = groupName;
        this.dramas = dramas;
    }
}
