package com.bc.iap.web.common.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 收藏信息VO
 */
@Data
public class FavoriteVo {

    /**
     * 收藏ID
     */
    private Long id;

    /**
     * 短剧ID
     */
    private Integer dramaId;

    /**
     * 短剧MD5ID
     */
    private String dramaMd5Id;

    /**
     * 短剧标题
     */
    private String dramaTitle;

    /**
     * 短剧封面图
     */
    private String dramaThumbnailUrl;

    /**
     * 短剧描述
     */
    private String dramaDescription;

    /**
     * 评分
     */
    private BigDecimal rating;

    /**
     * 点赞数
     */
    private Integer likesCount;

    /**
     * 浏览数
     */
    private Integer viewsCount;

    /**
     * 来源ID
     */
    private Integer sourceId;

    /**
     * 集数
     */
    private Integer episodes;

    /**
     * 收藏时间
     */
    private LocalDateTime createdAt;
}
