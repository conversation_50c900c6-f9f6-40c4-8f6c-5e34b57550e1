package com.bc.iap.web.common.utils;

import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;

import java.nio.file.Paths;

public class CodeGenerator {

    public static void main(String[] args) {
        FastAutoGenerator.create("**********************************************************************************************************************************************************", "umk-serv_rw", "5Bt#rFs[cA,pyHs")
                .globalConfig(builder -> builder
                        .outputDir(Paths.get(System.getProperty("user.dir")) + "/src/main/java")
                        .commentDate("yyyy-MM-dd")
                )
                .packageConfig(builder -> builder
                        .parent("com.bc.iap.user")
                        .entity("entity")
                        .mapper("mapper")
                        .serviceImpl("service")
                        .xml("mapper.xml")
                )
                .strategyConfig(builder -> builder
                        .addInclude("t_site_info")
                        .entityBuilder()
                        .enableLombok()
                )
                .templateEngine(new FreemarkerTemplateEngine())
                .execute();
    }


}
