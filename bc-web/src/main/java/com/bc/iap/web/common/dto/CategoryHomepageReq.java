package com.bc.iap.web.common.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 根据分类ID获取首页数据的请求类
 */
@Data
public class CategoryHomepageReq {
    
    /**
     * 站点域名
     */
    @NotNull(message = "siteDomain cannot be null")
    private String siteDomain = "vidfeedly.com";
    
    /**
     * 分类ID列表
     */
    @NotEmpty(message = "categoryIds cannot be empty")
    private List<Integer> categoryIds;
}
