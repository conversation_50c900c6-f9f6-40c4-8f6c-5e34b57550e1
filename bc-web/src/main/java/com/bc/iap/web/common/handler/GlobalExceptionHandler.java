package com.bc.iap.web.common.handler;


import com.batmobi.dataxsync.common.model.BaseResponse;
import com.bc.iap.common.dto.exception.BaseException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.validation.ValidationException;
import java.nio.file.AccessDeniedException;
import java.util.Locale;
import java.util.Set;

@ControllerAdvice
@ResponseBody
@Slf4j
public class GlobalExceptionHandler {

    @Autowired
    private MessageSource messageSource;

    @ExceptionHandler(BaseException.class)
    @ResponseStatus(HttpStatus.OK)
    public BaseResponse<?> handle(BaseException ex) {
        log.error(ex.getMessage());
        Locale locale = LocaleContextHolder.getLocale();
        String msg = messageSource.getMessage(ex.getMessage(), null, ex.getMessage(), locale);
        return new BaseResponse<>(ex.getCode(), msg, ex.getMessage());
    }

    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public BaseResponse<?> handle(Exception ex) {
        log.error(ex.getMessage(), ex);
        Locale locale = LocaleContextHolder.getLocale();
        String msg = messageSource.getMessage("server.internal.error", null, "Server internal error!", locale);
        return new BaseResponse<Object>(HttpStatus.INTERNAL_SERVER_ERROR.value(), msg, ex.getMessage());
    }

    private static String getErrorMsg(BindingResult result) {
        StringBuilder msg = new StringBuilder();
        if (result.hasFieldErrors()) {
            for (FieldError error : result.getFieldErrors()) {
                String simpleName = error.getField().substring(error.getField().lastIndexOf('.') + 1);
                msg.append(simpleName).append(":").append(error.getDefaultMessage());
            }
        } else if (result.hasErrors()) {
            for (ObjectError error : result.getAllErrors()) {
                msg.append(error.getDefaultMessage());
            }
        }
        log.error("{}", msg);
        return msg.toString();
    }

    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public BaseResponse<?> handle(BindException ex) {
        return new BaseResponse<>(HttpStatus.BAD_REQUEST.value(), getErrorMsg(ex.getBindingResult()), null);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public BaseResponse<?> handle(MethodArgumentNotValidException exception) {
        return new BaseResponse<>(HttpStatus.BAD_REQUEST.value(), getErrorMsg(exception.getBindingResult()), null);
    }

    @ExceptionHandler(ValidationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public BaseResponse<?> handle(ValidationException exception) {
        log.error(exception.getMessage());
        StringBuilder msg = new StringBuilder();
        if (exception instanceof ConstraintViolationException) {
            ConstraintViolationException exs = (ConstraintViolationException) exception;
            Set<ConstraintViolation<?>> violations = exs.getConstraintViolations();
            for (ConstraintViolation<?> item : violations) {
                String path = item.getPropertyPath().toString();
                String fieldName = path.substring(path.lastIndexOf(".") + 1);
                msg.append(String.format("%s", item.getMessage()));
                log.error("{} {}", fieldName, item.getMessage());
            }
        } else {
            msg.append(exception.getMessage());
        }
        return new BaseResponse<>(HttpStatus.BAD_REQUEST.value(), msg.toString(), null);
    }

    @ExceptionHandler(AccessDeniedException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public BaseResponse<?> handle(AccessDeniedException ex) {
        log.error(ex.getMessage(), ex);
        Locale locale = LocaleContextHolder.getLocale();
        String msg = messageSource.getMessage(ex.getMessage(), null, ex.getMessage(), locale);
        return new BaseResponse<>(HttpStatus.FORBIDDEN.value(), msg, null);
    }

}