package com.bc.iap.web;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@EnableAsync
@EnableScheduling
@ComponentScan({"com.bc.iap"})
@MapperScan({"com.bc.iap.*.mapper"})
public class BcIapWebApplication {
    public static void main(String[] args) {
        SpringApplication.run(BcIapWebApplication.class, args);
    }

}
