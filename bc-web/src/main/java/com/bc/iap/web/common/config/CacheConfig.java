package com.bc.iap.web.common.config;

import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.CacheManager;
import org.springframework.cache.caffeine.CaffeineCache;
import org.springframework.cache.support.CompositeCacheManager;
import org.springframework.cache.support.SimpleCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Configuration
@RequiredArgsConstructor
public class CacheConfig {

	private final RedisSerializer<Object> redisSerializer;
	private final RedisConnectionFactory redisConnectionFactory;

	private final Environment environment;


	/**
	 * 混合缓存处理器
	 */
	@Bean
	public CacheManager compositeCacheManager() {
		CompositeCacheManager compositeCacheManager = new CompositeCacheManager(redisCacheManager(), caffeineCacheManager());
		compositeCacheManager.setFallbackToNoOpCache(false);
		return compositeCacheManager;
	}

	/**
	 * redis缓存管理器
	 * */
	private RedisCacheManager redisCacheManager() {
		// 默认配置
		RedisCacheConfiguration defaultCacheConfig = getRedisCacheConfigurationWithTtl(600L);
		RedisCacheManager redisCm = RedisCacheManager.builder(redisConnectionFactory)
				.cacheDefaults(defaultCacheConfig)
				.withInitialCacheConfigurations(getRedisCacheConfigurationMap())
				.disableCreateOnMissingCache()   // 不存在则不创建新的缓存，才能实现内存缓存的查找
				.build();
		redisCm.initializeCaches(); //需要手工初始化，这里spring不会初始化
		return redisCm;
	}

	private Map<String, RedisCacheConfiguration> getRedisCacheConfigurationMap() {
		Map<String, RedisCacheConfiguration> redisCacheConfigurationMap = new HashMap<>();
		redisCacheConfigurationMap.put("appCache", this.getRedisCacheConfigurationWithTtl(300L));
		return redisCacheConfigurationMap;
	}

	
	private RedisCacheConfiguration getRedisCacheConfigurationWithTtl(Long seconds) {
		return RedisCacheConfiguration.defaultCacheConfig()
				.serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
				.serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(redisSerializer))
				.entryTtl(Duration.ofSeconds(seconds));
	}

	// 内存缓存管理器，类似guava
	private CacheManager caffeineCacheManager() {
		SimpleCacheManager cacheManager = new SimpleCacheManager();
		boolean isTest = StringUtils.containsIgnoreCase(environment.getActiveProfiles()[0], "test");
		int cacheMinutes = isTest ? 1 : 5;
		cacheManager.setCaches(Arrays.asList(
				new CaffeineCache("userCache", Caffeine.newBuilder().maximumSize(200000).expireAfterWrite(cacheMinutes, TimeUnit.MINUTES).build()),
				new CaffeineCache("accountAppCache", Caffeine.newBuilder().maximumSize(100).expireAfterWrite(10, TimeUnit.MINUTES).build()),
				// 系统配置
				new CaffeineCache("configCache", Caffeine.newBuilder().maximumSize(100).expireAfterWrite(cacheMinutes, TimeUnit.MINUTES).build())
		));
		cacheManager.initializeCaches();
		return cacheManager;
	}

}
