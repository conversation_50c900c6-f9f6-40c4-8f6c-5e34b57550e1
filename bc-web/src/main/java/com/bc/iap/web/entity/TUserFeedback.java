package com.bc.iap.web.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 用户反馈表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-19
 */
@Getter
@Setter
@TableName("t_user_feedback")
public class TUserFeedback implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 站内用户ID
     */
    private Long userId;

    /**
     * 站内用户名
     */
    private String username;

    /**
     * 用户姓名
     */
    private String name;

    /**
     * 用户姓氏
     */
    private String surname;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 反馈主题
     */
    private String subject;

    /**
     * 支持类型：Subscription、Features、Report a problem、Comment/Suggestion、Other
     */
    private String supportType;

    /**
     * 反馈消息内容
     */
    private String message;

    /**
     * 用户IP地址
     */
    private String ipAddress;

    /**
     * 用户代理信息
     */
    private String userAgent;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
