package com.bc.iap.web.common.controller;


import com.batmobi.dataxsync.common.model.BaseResponse;
import com.bc.iap.common.utils.DateUtils;
import com.bc.iap.common.utils.IpAddressUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;


@RestController
@RequiredArgsConstructor
public class CheckController {

    private final Environment env;
    private static final LocalDateTime START_TIME = LocalDateTime.now();


    @GetMapping("/health")
    public BaseResponse<?> health(HttpServletRequest request) {
        Map<String, Object> data = new HashMap<>();
        data.put("profiles", env.getActiveProfiles()[0]);
        data.put("start", START_TIME.format(DateUtils.FMT_DATE_TIME));
        data.put("now", LocalDateTime.now().format(DateUtils.FMT_DATE_TIME));
        data.put("ip", IpAddressUtils.getLocalIp());
        data.put("client", IpAddressUtils.getClientIpAddr(request));
        data.put("application", "djs-bc-iap");
        return BaseResponse.success(data);
    }

    @GetMapping("/check")
    public BaseResponse<?> check(HttpServletRequest request) {
        Map<String, Object> data = new HashMap<>();
        data.put("profiles", env.getActiveProfiles()[0]);
        data.put("start", START_TIME.format(DateUtils.FMT_DATE_TIME));
        data.put("now", LocalDateTime.now().format(DateUtils.FMT_DATE_TIME));
        data.put("ip", IpAddressUtils.getLocalIp());
        data.put("client", IpAddressUtils.getClientIpAddr(request));
        data.put("application", "djs-bc-iap");
        return BaseResponse.success(data);
    }

}
