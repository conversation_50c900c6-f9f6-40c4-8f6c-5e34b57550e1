package com.bc.iap.web.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 短剧标签关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-23
 */
@Getter
@Setter
@TableName("drama_tags")
public class DramaTags implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 短剧ID
     */
    private String dramaId;

    /**
     * 标签ID
     */
    private Integer tagId;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
