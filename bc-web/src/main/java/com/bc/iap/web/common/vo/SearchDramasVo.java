package com.bc.iap.web.common.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 搜索短剧结果VO
 */
@Data
public class SearchDramasVo {
    
    /**
     * 短剧MD5ID
     */
    private String md5Id;
    
    /**
     * 短剧ID
     */
    private Integer dramaId;
    
    /**
     * 短剧标题
     */
    private String title;
    
    /**
     * 短剧描述
     */
    private String description;
    
    /**
     * 封面图片URL
     */
    private String thumbnailUrl;
    
    /**
     * 评分
     */
    private BigDecimal rating;
    
    /**
     * 点赞数
     */
    private Integer likesCount;
    
    /**
     * 浏览数
     */
    private Integer viewsCount;
    
    /**
     * 来源ID
     */
    private Integer sourceId;
    
    /**
     * 集数
     */
    private Integer episodes;
    
    /**
     * 发布日期
     */
    private LocalDate releaseDate;
    
    /**
     * 热度
     */
    private Integer heat;
}
