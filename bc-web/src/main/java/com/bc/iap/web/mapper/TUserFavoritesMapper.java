package com.bc.iap.web.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bc.iap.web.common.vo.FavoriteVo;
import com.bc.iap.web.entity.TUserFavorites;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 用户收藏表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-19
 */
public interface TUserFavoritesMapper extends BaseMapper<TUserFavorites> {

    /**
     * 分页查询用户收藏列表（关联短剧详细信息）
     *
     * @param page 分页对象
     * @param userId 用户ID
     * @param sourceId 来源ID（可选）
     * @return 收藏列表
     */
    Page<FavoriteVo> selectUserFavoritesWithDrama(Page<FavoriteVo> page, 
                                                   @Param("userId") Long userId, 
                                                   @Param("sourceId") Integer sourceId);

    /**
     * 检查用户是否已收藏某个短剧
     *
     * @param userId 用户ID
     * @param dramaId 短剧ID
     * @return 收藏记录数量
     */
    int checkUserFavorite(@Param("userId") Long userId, @Param("dramaId") Integer dramaId);

    /**
     * 统计用户收藏总数
     *
     * @param userId 用户ID
     * @return 收藏总数
     */
    Long countUserFavorites(@Param("userId") Long userId);
}
