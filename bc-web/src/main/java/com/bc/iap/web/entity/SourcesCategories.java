package com.bc.iap.web.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 来源分类关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-18
 */
@Getter
@Setter
@TableName("sources_categories")
public class SourcesCategories implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 来源ID
     */
    private Integer sourceId;

    /**
     * 分类ID
     */
    private Integer categoryId;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
