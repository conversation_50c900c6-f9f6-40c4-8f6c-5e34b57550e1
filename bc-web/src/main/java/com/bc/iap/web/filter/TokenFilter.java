package com.bc.iap.web.filter;

import com.batmobi.dataxsync.common.model.BaseResponse;
import com.bc.iap.common.constant.ErrorCode;
import com.bc.iap.common.constant.MDCConstant;
import com.bc.iap.common.utils.IpAddressUtils;
import com.bc.iap.common.utils.ObjectsUtils;
import com.bc.iap.common.utils.RequestInfoThreadLocal;
import com.bc.iap.common.utils.ServletUtils;
import com.bc.iap.user.model.dto.UserRespVo;
import com.bc.iap.user.model.resp.AppInfoResp;
import com.bc.iap.user.model.resp.UserTokenResp;
import com.bc.iap.user.service.AccountAppService;
import com.bc.iap.user.service.UserService;
import com.bc.iap.user.utils.UserInfoThreadLocal;
import com.google.common.base.Strings;
import com.zz.common.util.IPUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@RequiredArgsConstructor
@Component
public class TokenFilter implements Filter {

    private final UserService userService;

    private final AccountAppService accountAppService;

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) servletRequest;

        String uri = request.getRequestURI();
        try {
            if (needAuthticaton(uri)) {
                String token = request.getHeader(HttpHeaders.AUTHORIZATION);
                Long userId = ensureLegalToken(token);

                if (userId == null) {
                    ServletUtils.returnJson(response, BaseResponse.fail(ErrorCode.NOT_ACCESS, "Authorization header do not had legal token"));
                    return;
                }
                UserRespVo userInfo = userService.getUser(userId);
                if (userInfo == null) {
                    ServletUtils.returnJson(response, BaseResponse.fail(ErrorCode.USER_NOT_EXIST, "Inexistence User"));
                    return;
                }
                if (Objects.equals(userInfo.getUserStatus(), 0)) {
                    ServletUtils.returnJson(response, BaseResponse.fail(ErrorCode.BLACK_LIST_USER, "BlackList User"));
                    return;
                }
                UserInfoThreadLocal.setUser(userInfo);
            }
            if (uri.startsWith("/api/v1/")) {
                parseReqeustInfo(request);
            }
            chain.doFilter(request, response);
        } finally {
            try {
                UserInfoThreadLocal.clear();
                RequestInfoThreadLocal.clear();
                // 只统计客户端请求, 用户有登录的接口
//                String appId = MDC.get(MDCConstant.APP);
//                if (StringUtils.isNotBlank(appId)) {
//                    int status = ((HttpServletResponse) response).getStatus();
//                    Tags tags = Tags.of("appid", appId)
//                            .and("uri", uri)
//                            .and("status", String.valueOf(status));
//                    Metrics.counter("http_server_requests_total", tags).increment();
//                }
            } catch (Exception e) {
                // it's unscientific
                log.error("doFilter, {}", e.getMessage(), e);
            }
        }

    }


    /**
     * 是否需要认证
     */
    public static boolean needAuthticaton(String uri) {
        boolean flag = false;

        // 模糊拦截的url列表
        List<String> containsList = Arrays.asList("/api/v1/payment", "/api/v1/user/favorites");
        flag =  containsList.stream().anyMatch(uri::contains);

        // 精确拦截的url列表
        List<String> equalsList = Arrays.asList("/api/v1/user/feedback", "/api/v1/user/feedback/recent");
        flag = flag || equalsList.stream().anyMatch(uri::equals);

        return flag;


//        if (uri.contains("/api/v1/user")
//                || uri.contains(PaymentConstant.PAYER_MAX_CALLBACK_PATH)  // payerMax打款回调接口,直接放行
//                || uri.contains(PaymentConstant.PAYPAL_CAPTURE_ORDER_URL)  // payerMax打款回调接口,直接放行
//        ) {
//            return false;
//        }
//        return
    }

    /**
     * 确保token合法
     */
    private Long ensureLegalToken(String token) {

        if (StringUtils.isEmpty(token)) {
            return null;
        }

        UserTokenResp userTokenResp = userService.getUserBaseInfoFromToken(token);
        if (userTokenResp == null || userTokenResp.getUid() == null || userTokenResp.getAppId() == null) {
            return null;
        }

        MDC.put(MDCConstant.APP, String.valueOf(userTokenResp.getAppId()));
        AppInfoResp appInfo = accountAppService.getAppInfo(userTokenResp.getAppId());
        if (appInfo == null) {
            log.warn("TokenFilter.ensureLegalToken,inexistence appId : {} ", userTokenResp.getAppId());
            return null;
        }

        return userTokenResp.getUid();
    }


    private void parseReqeustInfo(HttpServletRequest request) {
        // 优先取header中的ip参数, 获取不到则通过request获取客户端IP
        String ip = ObjectsUtils.defaultIfBlank(request.getParameter("ip"), () -> IpAddressUtils.getClientIpAddr(request));
        // 获取渠道
        String channel = Strings.isNullOrEmpty(request.getHeader("Channel-Code")) ?
                Strings.isNullOrEmpty(request.getHeader("channel_code")) ? ""
                        : request.getHeader("channel_code") : request.getHeader("Channel-Code");
        if ("organic".equalsIgnoreCase(channel)) {
            channel = "Organic";
        }
        String domain = ServletUtils.getDomain(request.getServerName());
        AppInfoResp appInfoResp = accountAppService.getAppInfo(domain);

        RequestInfoThreadLocal.RequestInfo requestInfo = RequestInfoThreadLocal.RequestInfo.builder()
                .ip(ip)
                .appId(Optional.ofNullable(appInfoResp).map(AppInfoResp::getAppId).orElse(0L))
                .country(IPUtils.getCountryCode(ip))
                .appVer(request.getHeader("appVer"))
                .channel(channel)
                .productVersionName(request.getHeader("productVersionName"))
                .build();
        RequestInfoThreadLocal.set(requestInfo);
    }

    /**
     * 判断本次请求的数据类型是否为json
     *
     * @param request request
     * @return boolean
     */
    private boolean isJson(HttpServletRequest request) {
        if (request.getContentType() != null) {
            return request.getContentType().equals(MediaType.APPLICATION_JSON_VALUE) ||
                    request.getContentType().equals(MediaType.APPLICATION_JSON_UTF8_VALUE);
        }

        return false;
    }
}
