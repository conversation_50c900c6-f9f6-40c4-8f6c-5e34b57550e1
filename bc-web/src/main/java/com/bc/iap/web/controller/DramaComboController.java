package com.bc.iap.web.controller;

import com.batmobi.dataxsync.common.model.BaseResponse;
import com.bc.iap.payment.service.DramaComboService;
import com.bc.iap.user.model.req.UserLoginReq;
import com.bc.iap.user.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/combo")
@Slf4j
@Validated
public class DramaComboController {

    private final DramaComboService dramaComboService;

    @RequestMapping("/info")
    public BaseResponse<?> dramaComboList() {
        return BaseResponse.success(dramaComboService.getComboInfoList());
    }
}
