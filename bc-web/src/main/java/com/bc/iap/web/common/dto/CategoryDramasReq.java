package com.bc.iap.web.common.dto;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 分类短剧请求类
 */
@Data
public class CategoryDramasReq {
    
    /**
     * 来源ID（必填）
     */
    @NotNull(message = "来源ID不能为空")
    private Integer sourceId;
    
    /**
     * 分类ID（可选，如果不传则查询所有分类）
     */
    private Integer categoryId;
    
    /**
     * 当前页码，默认为1
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;
    
    /**
     * 每页大小，默认为10
     */
    @Min(value = 1, message = "每页大小必须大于0")
    private Integer pageSize = 10;
}
