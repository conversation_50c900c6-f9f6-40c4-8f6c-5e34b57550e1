package com.bc.iap.web.controller;

import com.batmobi.dataxsync.common.model.BaseResponse;
import com.bc.iap.common.constant.ErrorCode;
import com.bc.iap.web.common.dto.*;
import com.bc.iap.web.common.vo.*;
import com.bc.iap.web.entity.Categories;
import com.bc.iap.web.entity.ShortDramas;
import com.bc.iap.web.service.IShortDramasService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 短剧相关接口控制器
 * 提供短剧首页数据、剧集信息、剧集详情等功能
 *
 * <AUTHOR>
 * @since 2025-09-05
 */
@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/short-dramas")
public class ShortDramasController {
    private final IShortDramasService service;

    /**
     * 获取首页数据
     * 返回按分类分组的短剧列表，用于首页展示
     *
     * @param req 首页请求参数，包含站点域名等信息
     * @return 包含分类短剧列表的首页数据
     */
    @PostMapping("/homepage")
    public BaseResponse<HomepageVo> getHomepage(@Valid @RequestBody HomepageReq req) {
        log.info("获取首页数据请求: {}", req);
        Map<String, List<Integer>> siteSection = new HashMap<>();
        siteSection.put("vidfeedly.com", Arrays.asList(9,10,11,12,13,14,15,16));
        siteSection.put("bingelet.com", Arrays.asList(8, 7, 6, 5, 4, 3, 2, 1));
        if (siteSection.get(req.getSiteDomain()) == null) {
            throw new RuntimeException("siteDomain is not exist");
        }
        HomepageVo result = service.homepage(siteSection.get(req.getSiteDomain()), req.getSiteDomain(), req.getSourceId());
        log.info("根据分类ID获取首页数据成功，返回{}个分类", result.getPlateList() != null ? result.getPlateList().size() : 0);
        return BaseResponse.success(result);
    }

    /*
      根据指定分类ID获取首页数据

      根据传入的分类ID列表，查询drama_categories表中的所有相关数据，
      然后关联short_dramas_v1表获取剧的信息并返回

      @param req 包含分类ID列表和站点域名的请求参数
     * @return 包含指定分类短剧列表的首页数据
     */
//    @PostMapping("/homepage/by-categories")
//    public BaseResponse<HomepageVo> getHomepageByCategoryIds(@Valid @RequestBody CategoryHomepageReq req) {
//        log.info("根据分类ID获取首页数据请求: categoryIds={}, siteDomain={}", req.getCategoryIds(), req.getSiteDomain());
//        HomepageVo result = service.getShortDramasByCategoryIds(req.getCategoryIds(), req.getSiteDomain(), req.getSourceId());
//        log.info("根据分类ID获取首页数据成功，返回{}个分类", result.getPlateList() != null ? result.getPlateList().size() : 0);
//        return BaseResponse.success(result);
//    }

    /**
     * 根据短剧MD5ID 或者 dramaId 查询剧集信息
     * 获取指定短剧的基本信息和剧集总数
     *
     * @param req 包含短剧MD5ID的请求参数
     * @return 短剧的剧集基本信息（标题、总集数等）
     */
    @PostMapping("/episodes")
    public BaseResponse<EpisodesVo> getEpisodes(@Valid @RequestBody EpisodesReq req) {
        log.info("查询剧集信息请求: md5Id={}", req.getMd5Id());
        EpisodesVo result = service.episodesInfo(req);
        log.info("剧集信息查询成功: title={}, totalCount={}", result.getTitle(), result.getTotalCount());
        return BaseResponse.success(result);
    }

    /**
     * 根据剧集ID和集数序号查询剧集详情
     * 获取指定剧集的详细信息，包括视频链接、时长等
     *
     * @param req 包含剧集ID和集数序号的请求参数
     * @return 剧集详细信息（视频链接、时长、观看次数等）
     */
    @PostMapping("/detail")
    public BaseResponse<?> getEpisodeDetail(@Valid @RequestBody EpisodesDetailReq req, @RequestHeader(value = "Authorization", required = false) String token) {
        log.info("查询剧集详情请求: dramaId={}, index={}", req.getDramaId(), req.getIndex());
        EpisodesDetailVo result = service.episodeDetailInfo(req, token);
        log.info("剧集详情查询成功: episodeId={}, title={}", result.getEpisodeId(), result.getTitle());
        if (result.getMd5Id() == null) {
            return BaseResponse.fail(ErrorCode.FAILURE);
        }
        return BaseResponse.success(result);
    }

    /**
     * 搜索短剧接口
     * 根据标题关键词搜索短剧，支持按来源ID筛选和分页
     *
     * @param req 搜索请求参数，包含关键词、来源ID、分页信息
     * @return 分页搜索结果
     */
    @PostMapping("/search")
    public BaseResponse<PageData<SearchDramasVo>> searchDramas(@Valid @RequestBody SearchDramasReq req) {
        log.info("搜索短剧请求: keyword={}, sourceId={}, pageNum={}, pageSize={}",
                req.getKeyword(), req.getSourceId(), req.getPageNum(), req.getPageSize());
        PageData<SearchDramasVo> result = service.searchDramasByTitle(req);
        log.info("搜索短剧成功: 关键词={}, 总记录数={}, 返回{}条记录",
                req.getKeyword(), result.getTotal(), result.getList().size());
        return BaseResponse.success(result);
    }

    /**
     * 分类短剧接口
     * 根据来源ID和分类ID查询短剧，支持分页
     * 如果不传分类ID，则查询该来源下所有分类的短剧
     *
     * @param req 分类查询请求参数，包含来源ID、分类ID、分页信息
     * @return 分页查询结果
     */
    @PostMapping("/category")
    public BaseResponse<PageData<CategoryDramasVo>> getDramasByCategory(@Valid @RequestBody CategoryDramasReq req) {
        log.info("分类查询短剧请求: sourceId={}, categoryId={}, pageNum={}, pageSize={}",
                req.getSourceId(), req.getCategoryId(), req.getPageNum(), req.getPageSize());
        PageData<CategoryDramasVo> result = service.getDramasByCategory(req);
        log.info("分类查询短剧成功: sourceId={}, categoryId={}, 总记录数={}, 返回{}条记录",
                req.getSourceId(), req.getCategoryId(), result.getTotal(), result.getList().size());
        return BaseResponse.success(result);
    }

    /**
     * 获取分类接口
     * 根据来源ID获取分类列表，不传sourceId则返回所有分类
     *
     * @param sourceId 来源ID（可选）
     * @return 分类列表
     */
    @GetMapping("/categories")
    public BaseResponse<List<Categories>> getCategories(@RequestParam(required = false) Integer sourceId) {
        log.info("获取分类请求: sourceId={}", sourceId);
        List<Categories> result = service.getCategories(sourceId);
        log.info("获取分类成功: sourceId={}, 共{}个分类", sourceId, result.size());
        return BaseResponse.success(result);
    }

    /**
     * 获取分类接口
     * 根据来源ID获取分类列表，不传sourceId则返回所有分类
     *
     * @param sourceId 来源ID（可选）
     * @return 分类列表
     */
    @GetMapping("/tags")
    public BaseResponse<List<Categories>> getTags(@RequestParam(required = false) Integer sourceId) {
        log.info("获取分类请求: sourceId={}", sourceId);
        List<Categories> result = service.getCategories(sourceId);
        log.info("获取分类成功: sourceId={}, 共{}个分类", sourceId, result.size());
        return BaseResponse.success(result);
    }

    /**
     * 根据分类ID列表查询剧列表
     * 支持传入多个分类ID，查询出对应分类下的所有剧列表
     *
     * @param req 包含分类ID列表的请求参数
     * @return 剧列表
     */
    @PostMapping("/by-categories")
    public BaseResponse<List<ShortDramas>> getDramasByCategoryIds(@Valid @RequestBody CategoryDramasListReq req) {
        log.info("根据分类ID列表查询剧列表请求: categoryIds={}, sourceId={}", req.getCategoryIds(), req.getSourceId());
        List<ShortDramas> result = service.getDramasByCategoryIds(req.getCategoryIds(), req.getSourceId());
        log.info("根据分类ID列表查询剧列表成功，返回{}条记录", result.size());
        return BaseResponse.success(result);
    }

    /**
     * 根据模块ID列表查询剧列表
     * 支持传入多个模块ID，查询出对应模块下的所有剧列表
     *
     * @param req 包含模块ID列表的请求参数
     * @return 剧列表
     */
    @PostMapping("/by-sections")
    public BaseResponse<List<ShortDramas>> getDramasBySectionIds(@Valid @RequestBody SectionDramasListReq req) {
        log.info("根据模块ID列表查询剧列表请求: sectionIds={}, sourceId={}", req.getSectionIds(), req.getSourceId());
        List<ShortDramas> result = service.getDramasBySectionIds(req.getSectionIds(), req.getSourceId());
        log.info("根据模块ID列表查询剧列表成功，返回{}条记录", result.size());
        return BaseResponse.success(result);
    }

    /**
     * 根据标签ID列表查询剧列表
     * 支持传入多个标签ID，查询出对应标签下的所有剧列表
     *
     * @param req 包含标签ID列表的请求参数
     * @return 剧列表
     */
    @PostMapping("/by-tags")
    public BaseResponse<List<ShortDramas>> getDramasByTagIds(@Valid @RequestBody TagDramasListReq req) {
        log.info("根据标签ID列表查询剧列表请求: tagIds={}, sourceId={}", req.getTagIds(), req.getSourceId());
        List<ShortDramas> result = service.getDramasByTagIds(req.getTagIds(), req.getSourceId());
        log.info("根据标签ID列表查询剧列表成功，返回{}条记录", result.size());
        return BaseResponse.success(result);
    }

}
