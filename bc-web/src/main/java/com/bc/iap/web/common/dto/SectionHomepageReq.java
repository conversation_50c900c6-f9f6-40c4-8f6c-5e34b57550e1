package com.bc.iap.web.common.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 根据分类ID获取首页数据的请求类
 */
@Data
public class SectionHomepageReq {
    
    /**
     * 站点域名
     */
    @NotNull(message = "siteDomain cannot be null")
    private String siteDomain = "vidfeedly.com";
    
    /**
     * 模块ID列表
     */
    @NotEmpty(message = "sectionIds cannot be empty")
    private List<Integer> sectionIds;
}
