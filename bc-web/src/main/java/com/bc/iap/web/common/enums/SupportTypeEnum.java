package com.bc.iap.web.common.enums;

import lombok.Getter;

/**
 * 用户反馈支持类型枚举
 */
@Getter
public enum SupportTypeEnum {
    
    SUBSCRIPTION("Subscription", "订阅相关"),
    FEATURES("Features", "功能相关"),
    REPORT_PROBLEM("Report a problem", "问题报告"),
    COMMENT_SUGGESTION("Comment/Suggestion", "意见建议"),
    OTHER("Other", "其他");

    private final String code;
    private final String description;

    SupportTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据code获取枚举
     */
    public static SupportTypeEnum getByCode(String code) {
        for (SupportTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 验证code是否有效
     */
    public static boolean isValidCode(String code) {
        return getByCode(code) != null;
    }
}
