package com.bc.iap.web.common.dto.resp;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class JuxingEpisodeResp {

    @J<PERSON><PERSON>ield(name = "code")
    private Integer code;
    @JSO<PERSON>ield(name = "msg")
    private String msg;
    @JSONField(name = "data")
    private List<DataDTO> data;

    @NoArgsConstructor
    @Data
    public static class DataDTO {
        @JSONField(name = "video_id")
        private Integer videoId;
        @JSONField(name = "episode_no")
        private Integer episodeNo;
        @JSONField(name = "episode_title")
        private String episodeTitle;
        @JSONField(name = "episode_img")
        private String episodeImg;
        @JSONField(name = "tcplayer_app_id")
        private String tcplayerAppId;
        @JSONField(name = "tcplayer_file_id")
        private String tcplayerFileId;
        @JSONField(name = "tcplayer_sign")
        private String tcplayerSign;
        @JSONField(name = "expire_time")
        private Long expireTime;
        @JSONField(name = "tcplayer_sign_265")
        private String tcplayerSign265;
        @JSONField(name = "tcplayer_sign_264")
        private String tcplayerSign264;
        @JSONField(name = "duration")
        private Integer duration;
    }
}
