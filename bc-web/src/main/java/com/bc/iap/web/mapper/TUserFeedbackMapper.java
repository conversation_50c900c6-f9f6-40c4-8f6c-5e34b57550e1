package com.bc.iap.web.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bc.iap.web.entity.TUserFeedback;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 用户反馈表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-19
 */
public interface TUserFeedbackMapper extends BaseMapper<TUserFeedback> {

    /**
     * 统计各支持类型的反馈数量
     */
    @Select("SELECT support_type, COUNT(*) as count FROM t_user_feedback GROUP BY support_type ORDER BY count DESC")
    List<Map<String, Object>> countBySupportType();

    /**
     * 统计指定时间范围内的反馈数量
     */
    @Select("SELECT COUNT(*) FROM t_user_feedback WHERE created_at BETWEEN #{startTime} AND #{endTime}")
    Long countByTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 统计今日反馈数量
     */
    @Select("SELECT COUNT(*) FROM t_user_feedback WHERE DATE(created_at) = CURDATE()")
    Long countToday();

    /**
     * 统计本月反馈数量
     */
    @Select("SELECT COUNT(*) FROM t_user_feedback WHERE YEAR(created_at) = YEAR(NOW()) AND MONTH(created_at) = MONTH(NOW())")
    Long countThisMonth();

    /**
     * 获取最近的反馈记录
     */
    @Select("SELECT * FROM t_user_feedback WHERE user_id = #{userId} ORDER BY created_at DESC LIMIT #{limit}")
    List<TUserFeedback> getRecentFeedbacks(@Param("userId") Long userId, @Param("limit") Integer limit);
}
