package com.bc.iap.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bc.iap.common.constant.ErrorCode;
import com.bc.iap.common.dto.exception.BaseException;
import com.bc.iap.web.common.dto.AddFavoriteReq;
import com.bc.iap.web.common.dto.GetFavoritesReq;
import com.bc.iap.web.common.dto.RemoveFavoriteReq;
import com.bc.iap.web.common.vo.FavoriteVo;
import com.bc.iap.web.common.vo.PageData;
import com.bc.iap.web.entity.ShortDramas;
import com.bc.iap.web.entity.TUserFavorites;
import com.bc.iap.web.mapper.ShortDramasMapper;
import com.bc.iap.web.mapper.TUserFavoritesMapper;
import com.bc.iap.web.service.IUserFavoritesService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * <p>
 * 用户收藏服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-19
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserFavoritesServiceImpl implements IUserFavoritesService {

    private final TUserFavoritesMapper userFavoritesMapper;
    private final ShortDramasMapper shortDramasMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addFavorite(Long userId, AddFavoriteReq req) {
        log.info("用户添加收藏: userId={}, dramaId={}",
                userId, req.getDramaId());

        try {
            // 检查是否已经收藏
            if (isFavorited(userId, req.getDramaId())) {
                log.warn("用户已收藏该短剧: userId={}, dramaId={}", userId, req.getDramaId());
                throw new BaseException(ErrorCode.FAILURE, "您已经收藏过这部短剧了");
            }

            // 查询短剧信息
            ShortDramas drama = shortDramasMapper.selectById(req.getDramaId());
            if (drama == null) {
                log.error("短剧不存在: dramaId={}", req.getDramaId());
                throw new BaseException(ErrorCode.FAILURE, "短剧不存在");
            }

            // 创建收藏记录
            TUserFavorites favorite = new TUserFavorites();
            favorite.setUserId(userId);
            favorite.setDramaId(req.getDramaId());
            favorite.setDramaMd5Id(drama.getMd5Id());
            favorite.setDramaTitle(drama.getTitle());
            favorite.setDramaThumbnailUrl(drama.getThumbnailUrl());
            favorite.setSourceId(drama.getSourceId());
            favorite.setCreatedAt(LocalDateTime.now());
            favorite.setUpdatedAt(LocalDateTime.now());

            int result = userFavoritesMapper.insert(favorite);
            
            if (result > 0) {
                log.info("用户收藏添加成功: userId={}, dramaId={}, favoriteId={}", 
                        userId, req.getDramaId(), favorite.getId());
                return true;
            } else {
                log.error("用户收藏添加失败: userId={}, dramaId={}", userId, req.getDramaId());
                return false;
            }
        } catch (BaseException e) {
            throw e;
        } catch (Exception e) {
            log.error("用户收藏添加异常: userId={}, dramaId={}, error={}", 
                    userId, req.getDramaId(), e.getMessage(), e);
            throw new BaseException(ErrorCode.FAILURE, "收藏失败，请稍后重试");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeFavorite(Long userId, RemoveFavoriteReq req) {
        log.info("用户删除收藏: userId={}, dramaId={}", userId, req.getDramaId());

        try {
            LambdaQueryWrapper<TUserFavorites> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TUserFavorites::getUserId, userId)
                       .eq(TUserFavorites::getDramaId, req.getDramaId());

            int result = userFavoritesMapper.delete(queryWrapper);
            
            if (result > 0) {
                log.info("用户收藏删除成功: userId={}, dramaId={}", userId, req.getDramaId());
                return true;
            } else {
                log.warn("用户收藏删除失败，可能未收藏: userId={}, dramaId={}", userId, req.getDramaId());
                return false;
            }
        } catch (Exception e) {
            log.error("用户收藏删除异常: userId={}, dramaId={}, error={}", 
                    userId, req.getDramaId(), e.getMessage(), e);
            throw new BaseException(ErrorCode.FAILURE, "取消收藏失败，请稍后重试");
        }
    }

    @Override
    public PageData<FavoriteVo> getUserFavorites(Long userId, GetFavoritesReq req) {
        log.info("查询用户收藏列表: userId={}, pageNum={}, pageSize={}, sourceId={}", 
                userId, req.getPageNum(), req.getPageSize(), req.getSourceId());

        // 创建分页对象
        Page<FavoriteVo> page = new Page<>(req.getPageNum(), req.getPageSize());
        
        // 执行分页查询
        Page<FavoriteVo> result = userFavoritesMapper.selectUserFavoritesWithDrama(page, userId, req.getSourceId());
        
        log.info("查询用户收藏列表完成: userId={}, 总记录数={}, 当前页={}, 每页大小={}", 
                userId, result.getTotal(), result.getCurrent(), result.getSize());
        
        // 构建返回结果
        return new PageData<>(
                (int) result.getCurrent(),
                (int) result.getSize(),
                result.getTotal(),
                (int) result.getPages(),
                result.getRecords().size(),
                result.getRecords()
        );
    }

    @Override
    public boolean isFavorited(Long userId, Integer dramaId) {
        int count = userFavoritesMapper.checkUserFavorite(userId, dramaId);
        return count > 0;
    }

    @Override
    public Long countUserFavorites(Long userId) {
        log.info("统计用户收藏总数: userId={}", userId);
        Long count = userFavoritesMapper.countUserFavorites(userId);
        log.info("用户收藏总数: userId={}, count={}", userId, count);
        return count;
    }
}
