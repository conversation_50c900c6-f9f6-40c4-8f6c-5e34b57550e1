package com.bc.iap.web.common.dto.resp;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class JuxingDramaResp {

    @J<PERSON><PERSON>ield(name = "code")
    private Integer code;
    @JSONField(name = "msg")
    private String msg;
    @JSONField(name = "data")
    private DataDTO data;
    @JSONField(name = "request_id")
    private String requestId;

    @NoArgsConstructor
    @Data
    public static class DataDTO {
        @JSONField(name = "page")
        private Integer page;
        @J<PERSON>NField(name = "page_size")
        private Integer pageSize;
        @JSONField(name = "total")
        private Integer total;
        @JSONField(name = "pages")
        private Integer pages;
        @JSONField(name = "list")
        private List<DramaDTO> list;
    }

    @NoArgsConstructor
    @Data
    public static class DramaDTO {
        @JSONField(name = "video_id")
        private Integer videoId;
        @JSONField(name = "master_video_id")
        private Integer masterVideoId;
        @JSO<PERSON>ield(name = "video_total")
        private Integer videoTotal;
        @JSO<PERSON>ield(name = "video_title")
        private String videoTitle;
        @JSONField(name = "logo_img")
        private String logoImg;
        @JSONField(name = "horizontal_logo_img")
        private String horizontalLogoImg;
        @JSONField(name = "introduce")
        private String introduce;
        @JSONField(name = "tag_list")
        private List<String> tagList;
        @JSONField(name = "year")
        private Integer year;
        @JSONField(name = "buy_episode")
        private Integer buyEpisode;
        @JSONField(name = "duration")
        private Integer duration;
        @JSONField(name = "publish_time")
        private Long publishTime;
        @JSONField(name = "is_recommend")
        private Integer isRecommend;
        @JSONField(name = "recommendation")
        private String recommendation;
        @JSONField(name = "language_type")
        private Integer languageType;
        @JSONField(name = "wx_registration_number")
        private String wxRegistrationNumber;
        @JSONField(name = "video_category_type")
        private Integer videoCategoryType;
        @JSONField(name = "is_end")
        private Integer isEnd;
        @JSONField(name = "is_new_video")
        private Integer isNewVideo;
    }
}
