package com.bc.iap.web.common.dto;

import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 用户反馈请求类
 */
@Data
public class UserFeedbackReq {

    /**
     * 用户姓名
     */
    @NotBlank(message = "姓名不能为空")
    private String name;

    /**
     * 用户姓氏
     */
    @NotBlank(message = "姓氏不能为空")
    private String surname;

    /**
     * 用户邮箱
     */
    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    private String email;

    /**
     * 反馈主题
     */
    @NotBlank(message = "反馈主题不能为空")
    private String subject;

    /**
     * 支持类型：Subscription、Features、Report a problem、Comment/Suggestion、Other
     */
    @NotBlank(message = "支持类型不能为空")
    @Pattern(regexp = "^(Subscription|Features|Report a problem|Comment/Suggestion|Other)$", 
             message = "支持类型必须是：Subscription、Features、Report a problem、Comment/Suggestion、Other 中的一种")
    private String supportType;

    /**
     * 反馈消息内容
     */
    @NotBlank(message = "反馈消息不能为空")
    private String message;
}
