spring:
  datasource:
    url: ***************************************************************************************************************************************************
    username: umk-serv_rw
    password: '5Bt#rFs[cA,pyHs'
    hikari:
      # 连接池最大连接数，默认是 10
      maximum-pool-size: 20
      # 链接超时时间，默认 30000(30 秒)
      connection-timeout: 3000000
      # 空闲连接存活最大时间，默认 600000(10 分钟)
      idle-timeout: 10000000
      # 连接将被测试活动的最大时间量
      validation-timeout: 5000
      # 此属性控制池中连接的最长生命周期，值 0 表示无限生命周期，默认 1800000(30 分钟)
      max-lifetime: 360000000
      # 连接到数据库时等待的最长时间(秒)
      login-timeout: 30
      # 池中维护的最小空闲连接数
      minimum-idle: 20
  redis:
    sentinel:
      master: mymaster
      nodes: 192.168.0.126:26379,192.168.0.126:26380
    lettuce:
      pool:
        max-active: 50
        max-wait: 5000
        max-idle: 50
        min-idle: 10
    timeout: 5000
    database: 5
    password: 'X0a#_2q5j4_mYQB_b840fc02d524045429941cc15f59e41cb7be6c52'
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

payment:
  paypal:
    api-url: https://api-m.sandbox.paypal.com
    paypal-client-id: AUIS0ycphm-jSvWZrelsCQjeGdTXO5ORFX2OIzTNSQH79SdEn4IUZyJHUou6Am5lhnHi8zHA_Ju2HHEc
    paypal-client-secret: ECDFxR7W8Ul7V8hNeVUCyN4YEZBrk0UlwBjJJ-IliZ4djbV8azSuWwyFY5rSVzz5TTFC96nwQQXrMvvS
    notify-url: https://test-api.vidfeedly.com
  payermax:
    api-url: https://pay-gate-uat.payermax.com
    merchant-id: SDP01010114442705
    app-id: 45b0344be2464c418b6f61a26387784f
    pm-public-key: 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAnzfzaEP0HtfLWdk+ZlFGSsRAMwcWxCgjU0zU2rpfxQxTFHnYJqOKJviYi30Y8/41WOR/mptmRbjw0aIQrffsljNt/g+gov/+sh8WM3gCTvbJ2z6oxKNYCUD3aKa8i4uHyb/Y/jwOLIBjOjrLQlyjhQQR0VE8AOX6wBy1ZamJgMhaIusUy1PARTCDTT1N+loNgOKDo6noIPz8gLI4YvmR+zn7/mvsvq+blF2zDHA5aMHGo+8oprKU9i6X1Jb94sWrWPhE67P0dm/cbDdlvlqc9a5EVXDuHs50TGkdTxeUfpNKoj2nk1HVqVs2nmPDOYVOaL9cQVwEBXRZaWY8wPEtOwIDAQAB'
    merchant-private-key: 'MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCfN/NoQ/Qe18tZ2T5mUUZKxEAzBxbEKCNTTNTaul/FDFMUedgmo4om+JiLfRjz/jVY5H+am2ZFuPDRohCt9+yWM23+D6Ci//6yHxYzeAJO9snbPqjEo1gJQPdopryLi4fJv9j+PA4sgGM6OstCXKOFBBHRUTwA5frAHLVlqYmAyFoi6xTLU8BFMINNPU36Wg2A4oOjqegg/PyAsjhi+ZH7Ofv+a+y+r5uUXbMMcDlowcaj7yimspT2LpfUlv3ixatY+ETrs/R2b9xsN2W+Wpz1rkRVcO4eznRMaR1PF5R+k0qiPaeTUdWpWzaeY8M5hU5ov1xBXAQFdFlpZjzA8S07AgMBAAECggEAft45cQJ8ujrfSVfG1ga7CQZ51W83n9QP2edl0GgjucLJTI6OQrObeNQr0TI8OOd9oLhqSUq8YqAsHA6UVdswxLp0UbYoHNn5G/uTWC2vQTZ6v92c4xr5Il6HBfeeU186MvNQt7wo2yLubk68+cMRdQM6I10kf97BQGVf0YMXEurNe6PK3hjiL+Y0F5X7H/U1aBnN1rXmqxNkW752MzCu6Y6tEg0td5k87ZkZ+Jmw2A6LJNClWGuIADcuViliiRVaAYY01qALicSrfV+Wv6CuA7ziqskWoasoLS0TvjIaXE/+1ATSdhyqjGSoKcavh71fHK8g0rgqv23ViKG086RXwQKBgQDpH5eTmARzqkPv8Aw95vFBgX47/Ur8cOVfHJGP7tGm7DWuMW6heSWdWK0hWDHFT/BwzyZw1wOVZEh9zbhBgD3L8TLBs4o/5uHEzSpdOMDsIW2PHHdyD9Hxh6QsjKXH5ePGHu+xqqfNnFgsBMF2773kooZ8rFpLgiJiUxbvO3ZzmwKBgQCu18I0Y0KDRqrYrsoDoRtnQ8vsYyDEpsNS0ovrWzlESH9kKANXQjtBbxRsz673tCr6CPHn5uV0jGhNcdzk5QxxjngC4SdRLvSRRm7EyVomTe1KE2tvaDQMkGrBGn/WJNqLPl2zByLKQLwD1ns5wrK5+EXbXyzc9AyeRVw967bW4QKBgAmQq0Z6CUy5orn4jngHvsRiTMynUulStIaXhH10XhPMQ1OXYaOsddej6M6icMBYzFPEnB4rJFF/4N2QkEvXuDFi/yMle7mtfnv1sxRKHNhTcsxZbak3bvTLDPhq+O5fx128rTcL+oCcNp3vwARiR9dWt8/rvnS0rTZ9lEZSmUSbAoGAJWaL6W7uNGyycSV6myokVYOgJJHT2Z0enSHiwlbCOkQT1BXnGQxW5plfUiNe9ye4JcIyFPxESxkZ2q8npGMGtWfXbi5BndEgic76NOgpe5Yh0z2yNqOgtqtR2rrTa7h4c17E5l35aDBOx7dipqlqV/Ou5c1HKICgDl8h6zD37KECgYEAyc/6xpVxCnJwqxA2EysidsESFX0DVjY+5tGZzR+9/BxOlp9GCZcn+3DvOLyEXmtZALuRnVy9nZPikDf+JsIGXIVgzzsoic6qJJPw9jMKpepZy/Abc4iGQN9vim+1BjGMJtMwpj6rFS7nkJ+Vsl2voxNJbTLxOA0DHhknaaL5Gy8='
    notify-url: https://test-api.vidfeedly.com
    ftp-url: upload.payermax.com
    ftp-port: 23223
    ftp-name: C2024071007505226E98
    ftp-pwd: PKmgZnY*K!JUxtw!%&
  proxy:
    ip: *************
    bak-ip: *************
    port: 64531
    username: zhizh_proxy
    password: '5xnR#OQy1Lnn0v09'