spring:
  datasource:
    url: ************************************************************************************************************************************************
    username: bc_rw
    password: 'lC7smURGu+kUaOzV'
    hikari:
      # 连接池最大连接数，默认是 10
      maximum-pool-size: 20
      # 链接超时时间，默认 30000(30 秒)
      connection-timeout: 3000000
      # 空闲连接存活最大时间，默认 600000(10 分钟)
      idle-timeout: 10000000
      # 连接将被测试活动的最大时间量
      validation-timeout: 5000
      # 此属性控制池中连接的最长生命周期，值 0 表示无限生命周期，默认 1800000(30 分钟)
      max-lifetime: 360000000
      # 连接到数据库时等待的最长时间(秒)
      login-timeout: 30
      # 池中维护的最小空闲连接数
      minimum-idle: 20
  redis:
    host: **********
    port: 6379
    timeout: 5000
    database: 0
    password: 'AVA7EQhw0yEvuZgs1Exg'
    lettuce:
      pool:
        max-active: 50
        max-wait: 5000
        max-idle: 50
        min-idle: 10
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl


payment:
  paypal:
    api-url: https://api-m.paypal.com
    paypal-client-id: AQ3klMYI6rzBC_XPmXm-4WjlrFsWL_0jB3-V460PdZzwOzpUXjCa7sICmnK-YcWswgM0HBsuUy3Jnd0G
    paypal-client-secret: EDjdAGRaAqaoS3lRygF4i1OszEvTWNu0PO_BQ9DXOuIW1TEwQ7aTHJzMktD15uzHhIWPCN4FBlRpKR27
    notify-url: https://api.vidfeedly.com
  payermax:
    api-url: https://pay-gate.payermax.com
    merchant-id: P01010117099931
    app-id: c7ca9c264be943a9885821f94eb1309e
    pm-public-key: 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAhcX4MMYq/NqxOkk4/yR0GHgUxIFzl7kLLeTSJkGbVMcI0v3fMcPKOlyTP9sMD0nWY0goIBCRykFynmQoPG0v2Y2IA0N8ESfEqRXAljKWb9s2taiXlvhFHzGhRdxKyf7BJlIwlzfNRwJfW9FhpnWMJ1upL5C8/H+5mTpSW2brO9jIkEESz03K1w8g+BlKBQu3ZRR0KZ5nhsyr9yrx9XYFUqo2N280AWqrJP0aTq747eTXkihBQWz126YAq2S17jbgissGCBUMMfCVBUVsbbzEejk7Ei7TOkWnYoFUIkGbDkrepgGv013U+mK1KirONZiiJDQ+5WvZhypWAo3fAh2eDQIDAQAB'
    merchant-private-key: 'MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCFxfgwxir82rE6STj/JHQYeBTEgXOXuQst5NImQZtUxwjS/d8xw8o6XJM/2wwPSdZjSCggEJHKQXKeZCg8bS/ZjYgDQ3wRJ8SpFcCWMpZv2za1qJeW+EUfMaFF3ErJ/sEmUjCXN81HAl9b0WGmdYwnW6kvkLz8f7mZOlJbZus72MiQQRLPTcrXDyD4GUoFC7dlFHQpnmeGzKv3KvH1dgVSqjY3bzQBaqsk/RpOrvjt5NeSKEFBbPXbpgCrZLXuNuCKywYIFQwx8JUFRWxtvMR6OTsSLtM6RadigVQiQZsOSt6mAa/TXdT6YrUqKs41mKIkND7la9mHKlYCjd8CHZ4NAgMBAAECggEABzMDS2Su1ZLTbOaZ6alFkjeMLpMk7Gpp/ptM8AAh4usfP3OoOl5mGMP4rLwWJxB7ODae6jLaFb918q5DOBVMOPIT+tD78VRpkgv4pFyumfB7PkWHevl7htU976uOXtaWAID12n2HT6YCCcf38GRtGYUmUsgAU7pv+54W5pJgYIUSwLIWjryCuV+0mw6TvPR35WyTbnW1ur8i6CY3bxdJJJsnLdooWbM92KUdoJhsU5d8PuszobCkU3VVDzNoNxt/RdiPcXnWyfzgrgYWJ5Cz9WR3WHsPCMskCfck2k3URNsEuFnmxJFnXecw93kmDcXohc/3fK2vpSD1vKFl3SnrAQKBgQDn4kz506D0gw93InneHU9/uuFoIJ8o9XD1V9HfzT+JgChIXPJ/pgu+oRt8GeAcMQxc79ISHml2ddmeabCZBS8rpTIHgLxUn40FN+c32zU+KZu2TvqGXBs8pnf6SbXR+UL8W6fCWq7LttsmsdmncsyPDTQn5qVBLr9x3IktfNarwQKBgQCTr4/XmT9r23rWkBM0xrHr5WSPHgksreShQyido5/I1zQ1WObzg68WnxpJFmRjhen7utZTXQ9ma/4jXHPrXxIhh1MTbhE0Iv+RCxSX5B95rbVYReWVPmXf4Ctn0mZKhTkgP9quXN88aZhVJ2DvrRj9srpQ3pvkkiZPYhty1z01TQKBgAWloTJEURsjRfEkycr688CskGi0wquArquK4/WLTgzPLiv094LcTFTsH3s6Nhb5qs9ol7kRPIhB/na5JC/VzHxHnZxJKkz1Rb3myp61m+pToRMjgT5cZ91mQG279c2TQGpc4Qz+oFsdrOCYXDCTvx+MtsRx/XxE4yymrkCJVcEBAoGAPbdA+gzddvoqFB3zGLvrwDOFWVVo6hXlFArXn0CwViKwe5aiNKMT+PUV6/MCkqB0jjCevgmGlPuOIULSYkrBbed6LQVzBQGXIQqrWWoISTXxMWlMLpj7w5mPKfy/qGaR38svXGwNEdbixLsHBEGmzuXwfDj6loMIpWXPKKePuYUCgYEAh86Xt24tHlyr81X3f7GKnVZFBuQklkzyF2H9ARkSzKW272Z8gXDgUhf0+PdPQGwLOJD7S2PZf21wUdhTaAvdgnub8N1mlAY1c3KMMnj0xwybQ+zwx2JwdnO4u4bml5KGsVqnLkbUGGUkab7jvkH04PJq/m+FsElDrvVBtQ9qUNQ='
    notify-url: https://api.vidfeedly.com
    ftp-url: upload.payermax.com
    ftp-port: 23223
    ftp-name: C2024071007505226E98
    ftp-pwd: PKmgZnY*K!JUxtw!%&
  proxy:
    ip: *************
    bak-ip: *************
    port: 64531
    username: zhizh_proxy
    password: '5xnR#OQy1Lnn0v09'