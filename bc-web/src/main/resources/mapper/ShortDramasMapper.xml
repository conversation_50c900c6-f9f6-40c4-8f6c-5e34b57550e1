<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bc.iap.web.mapper.ShortDramasMapper">
    <select id="homepageByCategoryIds" resultType="com.bc.iap.web.common.vo.HomepageVoCategoryName" parameterType="com.bc.iap.web.common.dto.CategoryHomepageReq">
        SELECT short_dramas_v1.*, drama_categories.category_id, categories.name
        FROM short_dramas_v1
        INNER JOIN drama_categories ON short_dramas_v1.md5_id = drama_categories.drama_id
        INNER JOIN categories ON drama_categories.category_id = categories.category_id
        WHERE categories.status = 1
        <if test="sourceId != null">
            AND short_dramas_v1.source_id = #{sourceId}
        </if>
        <if test="categoryIds != null and categoryIds.size() > 0">
            AND drama_categories.category_id IN
            <foreach collection="categoryIds" item="categoryId" open="(" separator="," close=")">
                #{categoryId}
            </foreach>
        </if>
        ORDER BY short_dramas_v1.drama_id
    </select>

    <select id="homepageBySectionIds" resultType="com.bc.iap.web.common.vo.HomepageVoSectionName" parameterType="com.bc.iap.web.common.dto.SectionHomepageReq">
        SELECT short_dramas_v1.*, drama_sections.section_id, sections.name
        FROM short_dramas_v1
        INNER JOIN drama_sections ON short_dramas_v1.md5_id = drama_sections.drama_id
        INNER JOIN sections ON drama_sections.section_id = sections.section_id
        WHERE sections.status = 1
        <if test="sourceId != null">
            AND short_dramas_v1.source_id = #{sourceId}
        </if>
        <if test="sectionIds != null and sectionIds.size() > 0">
            AND drama_sections.section_id IN
            <foreach collection="sectionIds" item="sectionId" open="(" separator="," close=")">
                #{sectionId}
            </foreach>
        </if>
        ORDER BY short_dramas_v1.drama_id
    </select>

    <select id="selectDetailByDramaId" resultType="com.bc.iap.web.common.vo.EpisodesDetailVo" parameterType="com.bc.iap.web.common.dto.EpisodesDetailReq">
        SELECT
            short_dramas_v1.md5_id,
            episodes_v1.episode_id,
            episodes_v1.drama_id,
            episodes_v1.episode_number,
            episodes_v1.title AS episode_title,
            short_dramas_v1.title AS drama_title,
            episodes_v1.release_date,
            episodes_v1.duration,
            short_dramas_v1.thumbnail_url,
            episodes_v1.video_url,
            short_dramas_v1.views_count,
            short_dramas_v1.likes_count,
            episodes_v1.word_url,
            short_dramas_v1.description,
            episodes_v1.is_vip,
            short_dramas_v1.source_id,
            short_dramas_v1.episodes AS total_count,
            (SELECT COUNT(*) FROM episodes_v1 WHERE drama_id = #{dramaId} AND episodes_v1.is_vip = 0) AS free_count
        FROM episodes_v1
                 LEFT JOIN short_dramas_v1
                            ON short_dramas_v1.drama_id = episodes_v1.drama_id
        WHERE episodes_v1.drama_id = #{dramaId}
          AND episodes_v1.episode_number = #{index}
    </select>

    <select id="selectByDramaId" parameterType="int" resultType="com.bc.iap.web.common.vo.EpisodesVo">
        SELECT
            short_dramas_v1.title,
            short_dramas_v1.drama_id,
            short_dramas_v1.thumbnail_url,
            short_dramas_v1.description,
            short_dramas_v1.rating,
            short_dramas_v1.views_count,
            short_dramas_v1.likes_count,
            short_dramas_v1.episodes as total_count,
            short_dramas_v1.episodes,
            (
                SELECT COUNT(*)
                FROM episodes_v1
                WHERE drama_id = #{dramaId}
                  AND episodes_v1.is_vip = 0
            ) AS free_count,
            short_dramas_v1.source_id
        FROM short_dramas_v1
                 LEFT JOIN episodes_v1
                            ON short_dramas_v1.drama_id = episodes_v1.drama_id
        WHERE short_dramas_v1.drama_id = #{dramaId}
    </select>

    <!-- 根据标题搜索短剧（支持分页） -->
    <select id="searchDramasByTitle" resultType="com.bc.iap.web.common.vo.SearchDramasVo">
        SELECT
            md5_id,
            drama_id,
            title,
            description,
            thumbnail_url,
            rating,
            likes_count,
            views_count,
            source_id,
            episodes,
            release_date,
            heat
        FROM short_dramas_v1
        WHERE title LIKE CONCAT('%', #{keyword}, '%')
        <if test="sourceId != null">
            AND source_id = #{sourceId}
        </if>
        ORDER BY heat DESC, views_count DESC, drama_id DESC
    </select>


    <!-- 根据分类ID列表查询剧列表 -->
    <select id="getDramasByCategoryIds" resultType="com.bc.iap.web.common.vo.HomepageVoCategoryName">
        SELECT short_dramas_v1.*, drama_categories.category_id, categories.name
        FROM short_dramas_v1
        INNER JOIN drama_categories ON short_dramas_v1.md5_id = drama_categories.drama_id
        INNER JOIN categories ON drama_categories.category_id = categories.category_id
        WHERE categories.status = 1
        <if test="sourceId != null">
            AND short_dramas_v1.source_id = #{sourceId}
        </if>
        <if test="categoryIds != null and categoryIds.size() > 0">
            AND drama_categories.category_id IN
            <foreach collection="categoryIds" item="categoryId" open="(" separator="," close=")">
                #{categoryId}
            </foreach>
        </if>
        ORDER BY short_dramas_v1.drama_id
    </select>

    <!-- 根据模块ID列表查询剧列表 -->
    <select id="getDramasBySectionIds" resultType="com.bc.iap.web.common.vo.HomepageVoSectionName">
        SELECT short_dramas_v1.*, drama_sections.section_id, sections.name
        FROM short_dramas_v1
        INNER JOIN drama_sections ON short_dramas_v1.md5_id = drama_sections.drama_id
        INNER JOIN sections ON drama_sections.section_id = sections.section_id
        WHERE sections.status = 1
        <if test="sourceId != null">
            AND short_dramas_v1.source_id = #{sourceId}
        </if>
        <if test="sectionIds != null and sectionIds.size() > 0">
            AND drama_sections.section_id IN
            <foreach collection="sectionIds" item="sectionId" open="(" separator="," close=")">
                #{sectionId}
            </foreach>
        </if>
        ORDER BY short_dramas_v1.drama_id
    </select>

    <!-- 根据标签ID列表查询剧列表 -->
    <select id="getDramasByTagIds" resultType="com.bc.iap.web.common.vo.HomepageVoTagName">
        SELECT short_dramas_v1.*, drama_tags.tag_id, tags.name
        FROM short_dramas_v1
        INNER JOIN drama_tags ON short_dramas_v1.md5_id = drama_tags.drama_id
        INNER JOIN tags ON drama_tags.tag_id = tags.tag_id
        WHERE tags.status = 1
        <if test="sourceId != null">
            AND short_dramas_v1.source_id = #{sourceId}
        </if>
        <if test="tagIds != null and tagIds.size() > 0">
            AND drama_tags.tag_id IN
            <foreach collection="tagIds" item="tagId" open="(" separator="," close=")">
                #{tagId}
            </foreach>
        </if>
        ORDER BY short_dramas_v1.drama_id
    </select>


</mapper>
