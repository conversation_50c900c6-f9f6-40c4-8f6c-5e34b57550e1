<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bc.iap.web.mapper.TUserFavoritesMapper">

    <!-- 分页查询用户收藏列表（关联短剧详细信息） -->
    <select id="selectUserFavoritesWithDrama" resultType="com.bc.iap.web.common.vo.FavoriteVo">
        SELECT 
            f.id,
            f.drama_id,
            f.drama_md5_id,
            f.drama_title,
            f.drama_thumbnail_url,
            s.description as drama_description,
            s.rating,
            s.likes_count,
            s.views_count,
            f.source_id,
            s.episodes,
            f.created_at
        FROM t_user_favorites f
        INNER JOIN short_dramas_v1 s ON f.drama_id = s.drama_id
        WHERE f.user_id = #{userId}
        <if test="sourceId != null">
            AND f.source_id = #{sourceId}
        </if>
        ORDER BY f.created_at DESC
    </select>

    <!-- 检查用户是否已收藏某个短剧 -->
    <select id="checkUserFavorite" resultType="int">
        SELECT COUNT(1)
        FROM t_user_favorites
        WHERE user_id = #{userId} AND drama_id = #{dramaId}
    </select>

    <!-- 统计用户收藏总数 -->
    <select id="countUserFavorites" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM t_user_favorites
        WHERE user_id = #{userId}
    </select>

</mapper>
