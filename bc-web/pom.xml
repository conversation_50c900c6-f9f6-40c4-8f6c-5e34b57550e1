<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.bc.iap</groupId>
        <artifactId>djs-bc-iap</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <name>bc-web</name>
    <description>bc-web</description>
    <artifactId>djs-bc-web</artifactId>
    <version>1.0-SNAPSHOT</version>
    <properties>
        <mysql.version>8.0.18</mysql.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.bc.iap</groupId>
            <artifactId>djs-bc-payment</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.bc.iap</groupId>
            <artifactId>djs-bc-user</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>${mysql.version}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <!-- 生成的jar中，不要包含pom.xml和pom.properties这两个文件 -->
                        <addMavenDescriptor>false</addMavenDescriptor>
                        <manifest>
                            <!-- 是否要把第三方jar放到manifest的classpath中 -->
                            <addClasspath>true</addClasspath>
                            <!-- 生成的manifest中classpath的前缀，因为要把第三方jar放到lib目录下，所以classpath的前缀是lib/ -->
                            <classpathPrefix>../lib/</classpathPrefix>
                            <!-- 应用的main class -->
                            <mainClass>com.bc.iap.web.BcIapWebApplication</mainClass>
                        </manifest>
                        <manifestEntries>
                            <Class-Path>../config/</Class-Path>
                        </manifestEntries>
                    </archive>
                    <!-- 过滤配置文件以便在外部修改 -->
                    <excludes>
                        <exclude>private/**/*</exclude>
                        <exclude>**/*.xml</exclude>
                        <exclude>**/*.properties</exclude>
                        <exclude>**/*.yml</exclude>
                        <exclude>**/*.sh</exclude>
                        <exclude>bin</exclude>
                        <exclude>mapper/**/*</exclude>
                        <exclude>mapper</exclude>
                        <exclude>sqltemplate/**/*</exclude>
                        <exclude>sqltemplate</exclude>
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-assembly-plugin</artifactId>
                <configuration>
                    <!-- not append assembly id in release file name -->
                    <appendAssemblyId>false</appendAssemblyId>
                    <descriptors>
                        <descriptor>src/main/resources/build.xml</descriptor>
                    </descriptors>
                </configuration>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-antrun-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy</id>
                        <phase>package</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <configuration>
                            <target name="copy and rename file">
                                <!--复制到umk-service下的target目录-->
                                <copy file="target/djs-bc-iap.tar.gz" tofile="../target/djs-bc-iap.tar.gz"
                                      overwrite="true"/>
                            </target>
                        </configuration>

                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>1.3.5</version>
                <configuration>
                    <verbose>true</verbose>
                    <overwrite>true</overwrite>
                    <configurationFile>src/main/resources/generator/generatorConfig.xml</configurationFile>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>mysql</groupId>
                        <artifactId>mysql-connector-java</artifactId>
                        <version>${mysql.version}</version>
                    </dependency>
                    <!--MyBatis Generator及工具 -->
                    <dependency>
                        <groupId>org.mybatis.generator</groupId>
                        <artifactId>mybatis-generator-core</artifactId>
                        <version>1.3.5</version>
                    </dependency>
                    <dependency>
                        <groupId>tk.mybatis</groupId>
                        <artifactId>mapper</artifactId>
                        <version>3.4.6</version>
                    </dependency>
                    <dependency>
                        <groupId>com.github.liuyuyu</groupId>
                        <artifactId>mbg-plus</artifactId>
                        <version>1.0</version>
                    </dependency>

                </dependencies>
            </plugin>
        </plugins>
    </build>

</project>
