-- 用户模块数据库迁移脚本
-- 执行前请备份数据库

-- 方案一：如果t_sys_user表已存在，重命名表并添加新字段
-- RENAME TABLE t_sys_user TO t_video_user;

-- 方案二：如果需要创建新表，使用以下脚本
CREATE TABLE IF NOT EXISTS t_video_user (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
    nickname VARCHAR(50) DEFAULT NULL COMMENT '昵称',
    username VARCHAR(50) NOT NULL COMMENT '用户名',
    email VARCHAR(100) DEFAULT NULL COMMENT '用户邮箱',
    password VARCHAR(255) DEFAULT NULL COMMENT '登录密码',
    phone VARCHAR(20) DEFAULT NULL COMMENT '电话号码',
    status SMALLINT DEFAULT 1 COMMENT '用户状态 0:禁用 1:正常',
    last_login_time DATETIME DEFAULT NULL COMMENT '上次登录时间',
    coins BIGINT DEFAULT 0 COMMENT '金币数量',
    reward_points BIGINT DEFAULT 0 COMMENT '奖励金数量',
    login_type INT DEFAULT 0 COMMENT '第三方登录类型 0:普通注册 1:Google 2:Facebook',
    third_party_id VARCHAR(255) DEFAULT NULL COMMENT '第三方登录唯一标识',
    avatar VARCHAR(500) DEFAULT NULL COMMENT '头像URL',
    email_verified INT DEFAULT 0 COMMENT '邮箱验证状态 0:未验证 1:已验证',
    email_verify_code VARCHAR(10) DEFAULT NULL COMMENT '邮箱验证码',
    email_verify_expire_time DATETIME DEFAULT NULL COMMENT '邮箱验证码过期时间',
    create_by VARCHAR(50) DEFAULT NULL COMMENT '创建人',
    update_by VARCHAR(50) DEFAULT NULL COMMENT '更新人',
    update_time DATETIME DEFAULT NULL COMMENT '更新时间',
    create_time DATETIME DEFAULT NULL COMMENT '创建时间',
    UNIQUE KEY uk_username (username),
    UNIQUE KEY uk_email (email),
    INDEX idx_third_party (login_type, third_party_id),
    INDEX idx_email_verify (email, email_verified),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='视频用户表';

-- 如果是从t_sys_user迁移数据，使用以下脚本
-- INSERT INTO t_video_user (id, nickname, username, email, password, phone, status, last_login_time, create_by, update_by, update_time, create_time, coins, reward_points, login_type, email_verified)
-- SELECT id, nickname, username, email, password, phone, status, last_login_time, create_by, update_by, update_time, create_time, 0, 0, 0, 1
-- FROM t_sys_user;

-- 验证数据迁移结果
SELECT
    COUNT(*) as total_users,
    SUM(CASE WHEN coins IS NOT NULL THEN 1 ELSE 0 END) as users_with_coins,
    SUM(CASE WHEN email_verified = 1 THEN 1 ELSE 0 END) as verified_users,
    SUM(CASE WHEN login_type = 0 THEN 1 ELSE 0 END) as normal_users
FROM t_video_user;

-- 显示表结构（用于验证）
DESCRIBE t_video_user;
