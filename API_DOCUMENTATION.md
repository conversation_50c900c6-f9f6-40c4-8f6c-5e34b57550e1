# 短剧查询接口文档

## 概述
实现了三个新的接口，支持根据分类ID列表、模块ID列表、标签ID列表查询对应的剧列表，返回JSON格式数据。

## 接口列表

### 1. 根据分类ID列表查询剧列表

**接口地址：** `POST /api/v1/short-dramas/dramas/by-categories`

**请求参数：**
```json
{
  "categoryIds": [1, 2, 3],
  "sourceId": 1
}
```

**参数说明：**
- `categoryIds`: 分类ID列表（必填）
- `sourceId`: 来源ID（可选）

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "dramaId": 1,
      "md5Id": "abc123",
      "title": "短剧标题",
      "description": "短剧描述",
      "country": "中国",
      "releaseDate": "2023-01-01",
      "rating": 8.5,
      "likesCount": 1000,
      "viewsCount": 50000,
      "thumbnailUrl": "http://example.com/image.jpg",
      "status": "released",
      "sourceId": 1,
      "heat": 100,
      "episodes": 20
    }
  ]
}
```

### 2. 根据模块ID列表查询剧列表

**接口地址：** `POST /api/v1/short-dramas/dramas/by-sections`

**请求参数：**
```json
{
  "sectionIds": [9, 10, 11],
  "sourceId": 1
}
```

**参数说明：**
- `sectionIds`: 模块ID列表（必填）
- `sourceId`: 来源ID（可选）

**响应格式：** 与分类查询接口相同

### 3. 根据标签ID列表查询剧列表

**接口地址：** `POST /api/v1/short-dramas/dramas/by-tags`

**请求参数：**
```json
{
  "tagIds": [1, 2, 3],
  "sourceId": 1
}
```

**参数说明：**
- `tagIds`: 标签ID列表（必填）
- `sourceId`: 来源ID（可选）

**响应格式：** 与分类查询接口相同

## 数据库表结构

### 主要表结构：
1. **categories** - 分类表
   - category_id: 分类ID
   - name: 分类名称
   - status: 状态（1-启用，0-禁用）

2. **drama_categories** - 分类与剧关联表
   - drama_id: 剧ID（对应short_dramas_v1.md5_id）
   - category_id: 分类ID

3. **sections** - 模块表
   - section_id: 模块ID
   - name: 模块名称
   - status: 状态（1-启用，0-禁用）

4. **drama_sections** - 模块与剧关联表
   - drama_id: 剧ID（对应short_dramas_v1.md5_id）
   - section_id: 模块ID

5. **tags** - 标签表
   - tag_id: 标签ID
   - name: 标签名称
   - status: 状态（1-启用，0-禁用）

6. **drama_tags** - 标签与剧关联表
   - drama_id: 剧ID（对应short_dramas_v1.md5_id）
   - tag_id: 标签ID

7. **short_dramas_v1** - 短剧主表
   - drama_id: 剧ID（自增主键）
   - md5_id: MD5唯一标识
   - title: 剧标题
   - 其他剧信息字段...

## 实现特点

1. **支持批量查询**：每个接口都支持传入多个ID进行批量查询
2. **可选来源过滤**：支持按来源ID过滤结果
3. **状态过滤**：只返回状态为启用的分类/模块/标签对应的剧
4. **JSON格式返回**：统一使用BaseResponse包装返回结果
5. **日志记录**：完整的请求和响应日志记录
